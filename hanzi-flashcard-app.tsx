import React, { useState, useEffect, useRef } from 'react';
import {
  BookOpen,
  Brain,
  RotateCcw,
  CheckCircle,
  XCircle,
  Star,
  Play,
  Pause,
  Volume2,
  Eye,
  PenTool,
  BarChart3,
  Settings,
  Home,
  Trophy,
  Clock,
  Target,
  Calendar
} from 'lucide-react';

// 汉字数据库 - 基于HSK等级和认知科学原理排序 (800个高频汉字)
const hanziDatabase = [
  // HSK 1级 - 基础高频字 (150字)
  { id: 1, character: '的', pinyin: 'de', meaning: 'possessive particle', components: ['白', '勺'], story: 'White spoon shows possession', level: 1, strokes: 8 },
  { id: 2, character: '一', pinyin: 'yī', meaning: 'one', components: ['一'], story: 'Single horizontal line', level: 1, strokes: 1 },
  { id: 3, character: '是', pinyin: 'shì', meaning: 'to be', components: ['日', '正'], story: 'Sun above correct = is', level: 1, strokes: 9 },
  { id: 4, character: '不', pinyin: 'bù', meaning: 'not', components: ['不'], story: 'Bird cannot fly upward', level: 1, strokes: 4 },
  { id: 5, character: '了', pinyin: 'le', meaning: 'completed action', components: ['了'], story: 'Person completed a task', level: 1, strokes: 2 },
  { id: 6, character: '人', pinyin: 'rén', meaning: 'person, people', components: ['人'], story: 'Looks like a person walking', level: 1, strokes: 2 },
  { id: 7, character: '我', pinyin: 'wǒ', meaning: 'I, me', components: ['戈', '扌'], story: 'Hand holding weapon = I', level: 1, strokes: 7 },
  { id: 8, character: '在', pinyin: 'zài', meaning: 'at, in', components: ['土', '才'], story: 'Talent on earth = located at', level: 1, strokes: 6 },
  { id: 9, character: '有', pinyin: 'yǒu', meaning: 'to have', components: ['月', '又'], story: 'Moon and hand = to have', level: 1, strokes: 6 },
  { id: 10, character: '他', pinyin: 'tā', meaning: 'he, him', components: ['亻', '也'], story: 'Person + also = he too', level: 1, strokes: 5 },
  { id: 11, character: '这', pinyin: 'zhè', meaning: 'this', components: ['辶', '文'], story: 'Walking to text = this', level: 1, strokes: 7 },
  { id: 12, character: '中', pinyin: 'zhōng', meaning: 'middle, center', components: ['中'], story: 'Arrow through center of target', level: 1, strokes: 4 },
  { id: 13, character: '大', pinyin: 'dà', meaning: 'big, large', components: ['大'], story: 'A person spreading arms wide', level: 1, strokes: 3 },
  { id: 14, character: '来', pinyin: 'lái', meaning: 'to come', components: ['来'], story: 'Wheat coming from earth', level: 1, strokes: 7 },
  { id: 15, character: '上', pinyin: 'shàng', meaning: 'up, above', components: ['上'], story: 'Line above the base', level: 1, strokes: 3 },
  { id: 16, character: '国', pinyin: 'guó', meaning: 'country, nation', components: ['囗', '玉'], story: 'Jade treasure within borders', level: 1, strokes: 8 },
  { id: 17, character: '个', pinyin: 'gè', meaning: 'individual, classifier', components: ['人', '丶'], story: 'Person with a dot', level: 1, strokes: 3 },
  { id: 18, character: '到', pinyin: 'dào', meaning: 'to arrive', components: ['至', '刂'], story: 'Reach with a knife = arrive', level: 1, strokes: 8 },
  { id: 19, character: '说', pinyin: 'shuō', meaning: 'to speak', components: ['讠', '兑'], story: 'Words + exchange = speak', level: 1, strokes: 9 },
  { id: 20, character: '们', pinyin: 'men', meaning: 'plural marker', components: ['亻', '门'], story: 'Person at door = plural', level: 1, strokes: 5 },
  { id: 21, character: '为', pinyin: 'wéi', meaning: 'for, because of', components: ['为'], story: 'Elephant working for others', level: 1, strokes: 4 },
  { id: 22, character: '子', pinyin: 'zǐ', meaning: 'child, son', components: ['子'], story: 'Baby with arms outstretched', level: 1, strokes: 3 },
  { id: 23, character: '和', pinyin: 'hé', meaning: 'and, with', components: ['禾', '口'], story: 'Grain and mouth = harmony', level: 1, strokes: 8 },
  { id: 24, character: '你', pinyin: 'nǐ', meaning: 'you', components: ['亻', '尔'], story: 'Person that is you', level: 1, strokes: 7 },
  { id: 25, character: '地', pinyin: 'dì', meaning: 'earth, ground', components: ['土', '也'], story: 'Soil that is earth', level: 1, strokes: 6 },
  { id: 26, character: '出', pinyin: 'chū', meaning: 'to go out', components: ['凵', '山'], story: 'Mountain coming out of valley', level: 1, strokes: 5 },
  { id: 27, character: '道', pinyin: 'dào', meaning: 'road, way', components: ['辶', '首'], story: 'Walking on the first path', level: 1, strokes: 12 },
  { id: 28, character: '也', pinyin: 'yě', meaning: 'also, too', components: ['也'], story: 'Snake slithering also', level: 1, strokes: 3 },
  { id: 29, character: '时', pinyin: 'shí', meaning: 'time', components: ['日', '寺'], story: 'Sun at temple = time', level: 1, strokes: 7 },
  { id: 30, character: '年', pinyin: 'nián', meaning: 'year', components: ['年'], story: 'Person carrying grain = harvest year', level: 1, strokes: 6 },
  { id: 31, character: '得', pinyin: 'de', meaning: 'to get, obtain', components: ['彳', '旦', '寸'], story: 'Step at dawn with hand = obtain', level: 1, strokes: 11 },
  { id: 32, character: '就', pinyin: 'jiù', meaning: 'then, immediately', components: ['京', '尤'], story: 'Capital especially = then', level: 1, strokes: 12 },
  { id: 33, character: '那', pinyin: 'nà', meaning: 'that', components: ['冄', '阝'], story: 'Two people by hill = that', level: 1, strokes: 6 },
  { id: 34, character: '要', pinyin: 'yào', meaning: 'to want', components: ['西', '女'], story: 'Woman wanting west', level: 1, strokes: 9 },
  { id: 35, character: '下', pinyin: 'xià', meaning: 'down, below', components: ['下'], story: 'Line below the base', level: 1, strokes: 3 },
  { id: 36, character: '以', pinyin: 'yǐ', meaning: 'with, by means of', components: ['以'], story: 'Person using tool', level: 1, strokes: 4 },
  { id: 37, character: '生', pinyin: 'shēng', meaning: 'to be born, life', components: ['生'], story: 'Plant growing from earth', level: 1, strokes: 5 },
  { id: 38, character: '会', pinyin: 'huì', meaning: 'can, meeting', components: ['人', '云'], story: 'People under clouds meeting', level: 1, strokes: 6 },
  { id: 39, character: '家', pinyin: 'jiā', meaning: 'home, family', components: ['宀', '豕'], story: 'Pig under roof means home', level: 1, strokes: 10 },
  { id: 40, character: '可', pinyin: 'kě', meaning: 'can, may', components: ['丁', '口'], story: 'Nail in mouth = can do', level: 1, strokes: 5 },
  { id: 41, character: '她', pinyin: 'tā', meaning: 'she, her', components: ['女', '也'], story: 'Woman that is she', level: 1, strokes: 6 },
  { id: 42, character: '里', pinyin: 'lǐ', meaning: 'inside, village', components: ['田', '土'], story: 'Fields and earth = village', level: 1, strokes: 7 },
  { id: 43, character: '后', pinyin: 'hòu', meaning: 'after, behind', components: ['彳', '口'], story: 'Step and mouth = behind', level: 1, strokes: 6 },
  { id: 44, character: '小', pinyin: 'xiǎo', meaning: 'small, little', components: ['小'], story: 'Three small dots getting smaller', level: 1, strokes: 3 },
  { id: 45, character: '么', pinyin: 'me', meaning: 'what, question particle', components: ['幺'], story: 'Tiny thread = what?', level: 1, strokes: 3 },
  { id: 46, character: '心', pinyin: 'xīn', meaning: 'heart, mind', components: ['心'], story: 'Heart with chambers', level: 1, strokes: 4 },
  { id: 47, character: '多', pinyin: 'duō', meaning: 'many, much', components: ['夕', '夕'], story: 'Two evenings = many', level: 1, strokes: 6 },
  { id: 48, character: '天', pinyin: 'tiān', meaning: 'day, sky', components: ['一', '大'], story: 'Big line above = sky', level: 1, strokes: 4 },
  { id: 49, character: '而', pinyin: 'ér', meaning: 'and, but', components: ['而'], story: 'Whiskers and = but', level: 1, strokes: 6 },
  { id: 50, character: '能', pinyin: 'néng', meaning: 'can, able', components: ['月', '匕', '匕'], story: 'Moon and two spoons = able', level: 1, strokes: 10 },

  // 继续HSK 1级
  { id: 51, character: '好', pinyin: 'hǎo', meaning: 'good, well', components: ['女', '子'], story: 'Woman and child = good', level: 1, strokes: 6 },
  { id: 52, character: '都', pinyin: 'dōu', meaning: 'all, both', components: ['者', '阝'], story: 'Person by city = all', level: 1, strokes: 10 },
  { id: 53, character: '然', pinyin: 'rán', meaning: 'so, like that', components: ['月', '犬', '灬'], story: 'Moon, dog, fire = natural', level: 1, strokes: 12 },
  { id: 54, character: '没', pinyin: 'méi', meaning: 'not have', components: ['氵', '殳'], story: 'Water without weapon = nothing', level: 1, strokes: 7 },
  { id: 55, character: '日', pinyin: 'rì', meaning: 'day, sun', components: ['日'], story: 'Square sun with center dot', level: 1, strokes: 4 },
  { id: 56, character: '对', pinyin: 'duì', meaning: 'correct, pair', components: ['又', '寸'], story: 'Hand and inch = correct', level: 1, strokes: 5 },
  { id: 57, character: '起', pinyin: 'qǐ', meaning: 'to rise, start', components: ['走', '己'], story: 'Walking oneself = to start', level: 1, strokes: 10 },
  { id: 58, character: '还', pinyin: 'hái', meaning: 'still, yet', components: ['辶', '不'], story: 'Walking not = still going', level: 1, strokes: 7 },
  { id: 59, character: '发', pinyin: 'fā', meaning: 'to send, emit', components: ['癶', '友'], story: 'Footsteps of friend = send', level: 1, strokes: 5 },
  { id: 60, character: '成', pinyin: 'chéng', meaning: 'to become', components: ['戊', '丁'], story: 'Weapon and nail = accomplish', level: 1, strokes: 6 },
  { id: 61, character: '事', pinyin: 'shì', meaning: 'matter, thing', components: ['一', '口', '亅'], story: 'Line, mouth, hook = matter', level: 1, strokes: 8 },
  { id: 62, character: '只', pinyin: 'zhǐ', meaning: 'only, just', components: ['口', '八'], story: 'Mouth and eight = only', level: 1, strokes: 5 },
  { id: 63, character: '作', pinyin: 'zuò', meaning: 'to do, make', components: ['亻', '乍'], story: 'Person working suddenly = to do', level: 1, strokes: 7 },
  { id: 64, character: '当', pinyin: 'dāng', meaning: 'when, should', components: ['彐', '田'], story: 'Snout over field = when', level: 1, strokes: 6 },
  { id: 65, character: '想', pinyin: 'xiǎng', meaning: 'to think', components: ['相', '心'], story: 'Face and heart = to think', level: 1, strokes: 13 },
  { id: 66, character: '看', pinyin: 'kàn', meaning: 'to see, look', components: ['手', '目'], story: 'Hand over eye = to look', level: 1, strokes: 9 },
  { id: 67, character: '文', pinyin: 'wén', meaning: 'text, culture', components: ['文'], story: 'Crossed lines = writing', level: 1, strokes: 4 },
  { id: 68, character: '无', pinyin: 'wú', meaning: 'without, none', components: ['无'], story: 'Person dancing = nothing', level: 1, strokes: 4 },
  { id: 69, character: '开', pinyin: 'kāi', meaning: 'to open', components: ['廾', '一'], story: 'Two hands lifting bar = open', level: 1, strokes: 4 },
  { id: 70, character: '手', pinyin: 'shǒu', meaning: 'hand', components: ['手'], story: 'Palm and fingers', level: 1, strokes: 4 },
  { id: 71, character: '十', pinyin: 'shí', meaning: 'ten', components: ['十'], story: 'Cross shape = ten', level: 1, strokes: 2 },
  { id: 72, character: '用', pinyin: 'yòng', meaning: 'to use', components: ['用'], story: 'Bucket for use', level: 1, strokes: 5 },
  { id: 73, character: '主', pinyin: 'zhǔ', meaning: 'main, master', components: ['丶', '王'], story: 'Dot over king = master', level: 1, strokes: 5 },
  { id: 74, character: '行', pinyin: 'xíng', meaning: 'to walk, ok', components: ['彳', '亍'], story: 'Left and right step = walk', level: 1, strokes: 6 },
  { id: 75, character: '方', pinyin: 'fāng', meaning: 'square, direction', components: ['方'], story: 'Square with dot = direction', level: 1, strokes: 4 },
  { id: 76, character: '又', pinyin: 'yòu', meaning: 'again, also', components: ['又'], story: 'Right hand = again', level: 1, strokes: 2 },
  { id: 77, character: '如', pinyin: 'rú', meaning: 'like, as', components: ['女', '口'], story: 'Woman\'s mouth = like', level: 1, strokes: 6 },
  { id: 78, character: '前', pinyin: 'qián', meaning: 'front, before', components: ['刖', '刂'], story: 'Foot cut off = front', level: 1, strokes: 9 },
  { id: 79, character: '所', pinyin: 'suǒ', meaning: 'place, that which', components: ['户', '斤'], story: 'Door and axe = place', level: 1, strokes: 8 },
  { id: 80, character: '本', pinyin: 'běn', meaning: 'book, origin', components: ['木', '一'], story: 'Tree with line = root/book', level: 1, strokes: 5 },
  { id: 81, character: '见', pinyin: 'jiàn', meaning: 'to see', components: ['目', '儿'], story: 'Eye with legs = to see', level: 1, strokes: 4 },
  { id: 82, character: '经', pinyin: 'jīng', meaning: 'through, classic', components: ['纟', '圣'], story: 'Thread and sage = classic', level: 1, strokes: 8 },
  { id: 83, character: '头', pinyin: 'tóu', meaning: 'head', components: ['大', '丶'], story: 'Big with dot = head', level: 1, strokes: 5 },
  { id: 84, character: '面', pinyin: 'miàn', meaning: 'face, surface', components: ['面'], story: 'Square face outline', level: 1, strokes: 9 },
  { id: 85, character: '公', pinyin: 'gōng', meaning: 'public, male', components: ['八', '厶'], story: 'Eight and private = public', level: 1, strokes: 4 },
  { id: 86, character: '同', pinyin: 'tóng', meaning: 'same, together', components: ['冂', '一', '口'], story: 'Frame with line and mouth = same', level: 1, strokes: 6 },
  { id: 87, character: '三', pinyin: 'sān', meaning: 'three', components: ['三'], story: 'Three horizontal lines', level: 1, strokes: 3 },
  { id: 88, character: '已', pinyin: 'yǐ', meaning: 'already', components: ['已'], story: 'Snake coiled = already done', level: 1, strokes: 3 },
  { id: 89, character: '老', pinyin: 'lǎo', meaning: 'old', components: ['老'], story: 'Person with bent back = old', level: 1, strokes: 6 },
  { id: 90, character: '从', pinyin: 'cóng', meaning: 'from, follow', components: ['人', '人'], story: 'Person following person', level: 1, strokes: 4 },
  { id: 91, character: '动', pinyin: 'dòng', meaning: 'to move', components: ['云', '力'], story: 'Cloud with force = movement', level: 1, strokes: 6 },
  { id: 92, character: '两', pinyin: 'liǎng', meaning: 'two, both', components: ['一', '冂', '山'], story: 'One frame two mountains = two', level: 1, strokes: 7 },
  { id: 93, character: '长', pinyin: 'cháng', meaning: 'long', components: ['长'], story: 'Long hair flowing = long', level: 1, strokes: 4 },
  { id: 94, character: '回', pinyin: 'huí', meaning: 'to return', components: ['囗', '口'], story: 'Mouth within frame = return', level: 1, strokes: 6 },
  { id: 95, character: '什', pinyin: 'shén', meaning: 'what', components: ['亻', '十'], story: 'Person and ten = what', level: 1, strokes: 4 },
  { id: 96, character: '二', pinyin: 'èr', meaning: 'two', components: ['二'], story: 'Two horizontal lines', level: 1, strokes: 2 },
  { id: 97, character: '水', pinyin: 'shuǐ', meaning: 'water', components: ['水'], story: 'Flowing water with drops', level: 1, strokes: 4 },
  { id: 98, character: '新', pinyin: 'xīn', meaning: 'new', components: ['亲', '斤'], story: 'Close with axe = new cut', level: 1, strokes: 13 },
  { id: 99, character: '手', pinyin: 'shǒu', meaning: 'hand', components: ['手'], story: 'Palm with fingers', level: 1, strokes: 4 },
  { id: 100, character: '高', pinyin: 'gāo', meaning: 'tall, high', components: ['亠', '口', '冂', '口'], story: 'Tower with doors = high', level: 1, strokes: 10 },

  // HSK 2级开始 (150字)
  { id: 101, character: '学', pinyin: 'xué', meaning: 'study, learn', components: ['学'], story: 'Child under roof learning', level: 2, strokes: 8 },
  { id: 102, character: '自', pinyin: 'zì', meaning: 'self, from', components: ['自'], story: 'Nose pointing to self', level: 2, strokes: 6 },
  { id: 103, character: '分', pinyin: 'fēn', meaning: 'divide, minute', components: ['八', '刀'], story: 'Eight divided by knife', level: 2, strokes: 4 },
  { id: 104, character: '总', pinyin: 'zǒng', meaning: 'total, always', components: ['悤', '心'], story: 'Hurried heart = always total', level: 2, strokes: 9 },
  { id: 105, character: '给', pinyin: 'gěi', meaning: 'to give', components: ['纟', '合'], story: 'Thread coming together = give', level: 2, strokes: 9 },
  { id: 106, character: '身', pinyin: 'shēn', meaning: 'body', components: ['身'], story: 'Pregnant person = body', level: 2, strokes: 7 },
  { id: 107, character: '此', pinyin: 'cǐ', meaning: 'this, here', components: ['止', '匕'], story: 'Stop and spoon = this', level: 2, strokes: 6 },
  { id: 108, character: '其', pinyin: 'qí', meaning: 'his, her, its', components: ['其'], story: 'Basket woven = its', level: 2, strokes: 8 },
  { id: 109, character: '安', pinyin: 'ān', meaning: 'safe, peaceful', components: ['宀', '女'], story: 'Woman under roof = safe', level: 2, strokes: 6 },
  { id: 110, character: '今', pinyin: 'jīn', meaning: 'now, today', components: ['人', '一'], story: 'Person over line = now', level: 2, strokes: 4 },
  { id: 111, character: '次', pinyin: 'cì', meaning: 'time, order', components: ['冫', '欠'], story: 'Ice and yawn = next time', level: 2, strokes: 6 },
  { id: 112, character: '使', pinyin: 'shǐ', meaning: 'to use, make', components: ['亻', '吏'], story: 'Person as official = to use', level: 2, strokes: 8 },
  { id: 113, character: '间', pinyin: 'jiān', meaning: 'between, room', components: ['门', '日'], story: 'Sun through door = between', level: 2, strokes: 7 },
  { id: 114, character: '理', pinyin: 'lǐ', meaning: 'reason, logic', components: ['王', '里'], story: 'King in village = reason', level: 2, strokes: 11 },
  { id: 115, character: '明', pinyin: 'míng', meaning: 'bright, clear', components: ['日', '月'], story: 'Sun and moon = bright', level: 2, strokes: 8 },
  { id: 116, character: '性', pinyin: 'xìng', meaning: 'nature, character', components: ['忄', '生'], story: 'Heart and birth = nature', level: 2, strokes: 8 },
  { id: 117, character: '知', pinyin: 'zhī', meaning: 'to know', components: ['矢', '口'], story: 'Arrow to mouth = to know', level: 2, strokes: 8 },
  { id: 118, character: '国', pinyin: 'guó', meaning: 'country', components: ['囗', '玉'], story: 'Jade within borders', level: 2, strokes: 8 },
  { id: 119, character: '意', pinyin: 'yì', meaning: 'meaning, intention', components: ['立', '日', '心'], story: 'Stand, sun, heart = meaning', level: 2, strokes: 13 },

  // 继续HSK 2级
  { id: 120, character: '问', pinyin: 'wèn', meaning: 'to ask', components: ['门', '口'], story: 'Mouth at door = to ask', level: 2, strokes: 6 },
  { id: 121, character: '很', pinyin: 'hěn', meaning: 'very', components: ['彳', '艮'], story: 'Step with determination = very', level: 2, strokes: 9 },
  { id: 122, character: '进', pinyin: 'jìn', meaning: 'to enter', components: ['辶', '井'], story: 'Walking to well = enter', level: 2, strokes: 7 },
  { id: 123, character: '种', pinyin: 'zhǒng', meaning: 'kind, type', components: ['禾', '中'], story: 'Grain in center = type', level: 2, strokes: 9 },
  { id: 124, character: '将', pinyin: 'jiāng', meaning: 'will, shall', components: ['丬', '夕', '寸'], story: 'Bed, evening, hand = will', level: 2, strokes: 9 },
  { id: 125, character: '各', pinyin: 'gè', meaning: 'each, every', components: ['夂', '口'], story: 'Walking to mouth = each', level: 2, strokes: 6 },
  { id: 126, character: '重', pinyin: 'zhòng', meaning: 'heavy, important', components: ['千', '里'], story: 'Thousand miles = heavy', level: 2, strokes: 9 },
  { id: 127, character: '线', pinyin: 'xiàn', meaning: 'line, thread', components: ['纟', '戋'], story: 'Thread cut small = line', level: 2, strokes: 8 },
  { id: 128, character: '内', pinyin: 'nèi', meaning: 'inside, within', components: ['冂', '人'], story: 'Person in frame = inside', level: 2, strokes: 4 },
  { id: 129, character: '数', pinyin: 'shù', meaning: 'number, count', components: ['米', '攵'], story: 'Rice being counted = number', level: 2, strokes: 13 },
  { id: 130, character: '正', pinyin: 'zhèng', meaning: 'correct, right', components: ['一', '止'], story: 'One stop = correct', level: 2, strokes: 5 },
  { id: 131, character: '反', pinyin: 'fǎn', meaning: 'opposite, reverse', components: ['厂', '又'], story: 'Cliff and hand = reverse', level: 2, strokes: 4 },
  { id: 132, character: '任', pinyin: 'rèn', meaning: 'to allow, duty', components: ['亻', '壬'], story: 'Person with burden = duty', level: 2, strokes: 6 },
  { id: 133, character: '件', pinyin: 'jiàn', meaning: 'item, piece', components: ['亻', '牛'], story: 'Person and cow = item', level: 2, strokes: 6 },
  { id: 134, character: '因', pinyin: 'yīn', meaning: 'because, cause', components: ['囗', '大'], story: 'Big in frame = because', level: 2, strokes: 6 },
  { id: 135, character: '定', pinyin: 'dìng', meaning: 'fixed, certain', components: ['宀', '正'], story: 'Correct under roof = fixed', level: 2, strokes: 8 },
  { id: 136, character: '机', pinyin: 'jī', meaning: 'machine, opportunity', components: ['木', '几'], story: 'Wood table = machine', level: 2, strokes: 6 },
  { id: 137, character: '工', pinyin: 'gōng', meaning: 'work, labor', components: ['工'], story: 'Tool for work', level: 2, strokes: 3 },
  { id: 138, character: '调', pinyin: 'diào', meaning: 'to adjust, tune', components: ['讠', '周'], story: 'Words around = adjust', level: 2, strokes: 10 },
  { id: 139, character: '并', pinyin: 'bìng', meaning: 'and, together', components: ['并'], story: 'Two people together', level: 2, strokes: 6 },
  { id: 140, character: '外', pinyin: 'wài', meaning: 'outside, foreign', components: ['夕', '卜'], story: 'Evening divination = outside', level: 2, strokes: 5 },
  { id: 141, character: '者', pinyin: 'zhě', meaning: 'person, one who', components: ['老', '日'], story: 'Old sun = wise person', level: 2, strokes: 8 },
  { id: 142, character: '相', pinyin: 'xiāng', meaning: 'mutual, each other', components: ['木', '目'], story: 'Tree and eye = observe each other', level: 2, strokes: 9 },
  { id: 143, character: '管', pinyin: 'guǎn', meaning: 'to manage, tube', components: ['竹', '官'], story: 'Bamboo official = manage', level: 2, strokes: 14 },
  { id: 144, character: '思', pinyin: 'sī', meaning: 'to think', components: ['田', '心'], story: 'Field and heart = think', level: 2, strokes: 9 },
  { id: 145, character: '位', pinyin: 'wèi', meaning: 'position, place', components: ['亻', '立'], story: 'Person standing = position', level: 2, strokes: 7 },
  { id: 146, character: '教', pinyin: 'jiāo', meaning: 'to teach', components: ['孝', '攵'], story: 'Filial piety taught = teach', level: 2, strokes: 11 },
  { id: 147, character: '广', pinyin: 'guǎng', meaning: 'wide, broad', components: ['广'], story: 'Shelter extending = wide', level: 2, strokes: 3 },
  { id: 148, character: '首', pinyin: 'shǒu', meaning: 'head, first', components: ['首'], story: 'Head with hair = first', level: 2, strokes: 9 },
  { id: 149, character: '电', pinyin: 'diàn', meaning: 'electricity', components: ['雨', '电'], story: 'Rain and lightning = electricity', level: 2, strokes: 5 },
  { id: 150, character: '常', pinyin: 'cháng', meaning: 'often, usual', components: ['尚', '巾'], story: 'Still cloth = often', level: 2, strokes: 11 },

  // HSK 3级开始 (300字)
  { id: 151, character: '路', pinyin: 'lù', meaning: 'road, path', components: ['足', '各'], story: 'Foot going each way = road', level: 3, strokes: 13 },
  { id: 152, character: '科', pinyin: 'kē', meaning: 'science, subject', components: ['禾', '斗'], story: 'Grain and measure = science', level: 3, strokes: 9 },
  { id: 153, character: '去', pinyin: 'qù', meaning: 'to go', components: ['土', '厶'], story: 'Earth and private = go', level: 3, strokes: 5 },
  { id: 154, character: '力', pinyin: 'lì', meaning: 'strength, power', components: ['力'], story: 'Muscle flexing = strength', level: 3, strokes: 2 },
  { id: 155, character: '神', pinyin: 'shén', meaning: 'god, spirit', components: ['示', '申'], story: 'Show and extend = god', level: 3, strokes: 9 },
  { id: 156, character: '半', pinyin: 'bàn', meaning: 'half', components: ['八', '牛'], story: 'Eight cow = half', level: 3, strokes: 5 },
  { id: 157, character: '火', pinyin: 'huǒ', meaning: 'fire', components: ['火'], story: 'Flames rising up', level: 3, strokes: 4 },
  { id: 158, character: '南', pinyin: 'nán', meaning: 'south', components: ['十', '冂', '干'], story: 'Ten in frame dry = south', level: 3, strokes: 9 },
  { id: 159, character: '写', pinyin: 'xiě', meaning: 'to write', components: ['冖', '与'], story: 'Cover and give = write', level: 3, strokes: 5 },
  { id: 160, character: '部', pinyin: 'bù', meaning: 'part, section', components: ['立', '口', '阝'], story: 'Stand mouth city = part', level: 3, strokes: 10 },
  { id: 161, character: '等', pinyin: 'děng', meaning: 'to wait, class', components: ['竹', '寺'], story: 'Bamboo temple = wait', level: 3, strokes: 12 },
  { id: 162, character: '住', pinyin: 'zhù', meaning: 'to live, stay', components: ['亻', '主'], story: 'Person master = live', level: 3, strokes: 7 },
  { id: 163, character: '百', pinyin: 'bǎi', meaning: 'hundred', components: ['一', '白'], story: 'One white = hundred', level: 3, strokes: 6 },
  { id: 164, character: '改', pinyin: 'gǎi', meaning: 'to change', components: ['己', '攵'], story: 'Self being hit = change', level: 3, strokes: 7 },
  { id: 165, character: '先', pinyin: 'xiān', meaning: 'first, before', components: ['儿', '土'], story: 'Child on earth = first', level: 3, strokes: 6 },
  { id: 166, character: '风', pinyin: 'fēng', meaning: 'wind', components: ['几', '虫'], story: 'Table insect = wind', level: 3, strokes: 4 },
  { id: 167, character: '议', pinyin: 'yì', meaning: 'to discuss', components: ['讠', '义'], story: 'Words of righteousness = discuss', level: 3, strokes: 5 },
  { id: 168, character: '往', pinyin: 'wǎng', meaning: 'to go towards', components: ['彳', '王'], story: 'Step to king = go towards', level: 3, strokes: 8 },
  { id: 169, character: '元', pinyin: 'yuán', meaning: 'first, dollar', components: ['一', '兀'], story: 'One sitting = first', level: 3, strokes: 4 },
  { id: 170, character: '办', pinyin: 'bàn', meaning: 'to handle', components: ['力', '办'], story: 'Strength to handle', level: 3, strokes: 4 },
  { id: 171, character: '民', pinyin: 'mín', meaning: 'people, citizen', components: ['民'], story: 'Eye with line = people', level: 3, strokes: 5 },
  { id: 172, character: '结', pinyin: 'jié', meaning: 'to tie, result', components: ['纟', '吉'], story: 'Thread lucky = tie', level: 3, strokes: 9 },
  { id: 173, character: '解', pinyin: 'jiě', meaning: 'to solve', components: ['角', '刀', '牛'], story: 'Horn knife cow = solve', level: 3, strokes: 13 },
  { id: 174, character: '步', pinyin: 'bù', meaning: 'step, pace', components: ['止', '少'], story: 'Stop few = step', level: 3, strokes: 7 },
  { id: 175, character: '确', pinyin: 'què', meaning: 'certain, sure', components: ['石', '隹'], story: 'Stone bird = certain', level: 3, strokes: 12 },
  { id: 176, character: '级', pinyin: 'jí', meaning: 'level, grade', components: ['纟', '及'], story: 'Thread reach = level', level: 3, strokes: 6 },
  { id: 177, character: '参', pinyin: 'cān', meaning: 'to participate', components: ['厶', '大', '彡'], story: 'Private big hair = participate', level: 3, strokes: 8 },
  { id: 178, character: '绝', pinyin: 'jué', meaning: 'absolute, cut off', components: ['纟', '色'], story: 'Thread color = absolute', level: 3, strokes: 9 },
  { id: 179, character: '愿', pinyin: 'yuàn', meaning: 'to wish', components: ['原', '心'], story: 'Original heart = wish', level: 3, strokes: 14 },
  { id: 180, character: '技', pinyin: 'jì', meaning: 'skill, technique', components: ['扌', '支'], story: 'Hand support = skill', level: 3, strokes: 7 },
  { id: 181, character: '建', pinyin: 'jiàn', meaning: 'to build', components: ['廴', '聿'], story: 'Walk and brush = build', level: 3, strokes: 8 },
  { id: 182, character: '空', pinyin: 'kōng', meaning: 'empty, sky', components: ['穴', '工'], story: 'Cave work = empty', level: 3, strokes: 8 },
  { id: 183, character: '合', pinyin: 'hé', meaning: 'to combine', components: ['人', '一', '口'], story: 'Person one mouth = combine', level: 3, strokes: 6 },
  { id: 184, character: '目', pinyin: 'mù', meaning: 'eye', components: ['目'], story: 'Eye looking sideways', level: 3, strokes: 5 },
  { id: 185, character: '领', pinyin: 'lǐng', meaning: 'to lead', components: ['令', '页'], story: 'Order page = lead', level: 3, strokes: 11 },
  { id: 186, character: '呢', pinyin: 'ne', meaning: 'question particle', components: ['口', '尼'], story: 'Mouth nun = question', level: 3, strokes: 8 },
  { id: 187, character: '味', pinyin: 'wèi', meaning: 'taste, flavor', components: ['口', '未'], story: 'Mouth not yet = taste', level: 3, strokes: 8 },
  { id: 188, character: '指', pinyin: 'zhǐ', meaning: 'finger, to point', components: ['扌', '旨'], story: 'Hand purpose = point', level: 3, strokes: 9 },
  { id: 189, character: '月', pinyin: 'yuè', meaning: 'moon, month', components: ['月'], story: 'Crescent moon', level: 3, strokes: 4 },
  { id: 190, character: '言', pinyin: 'yán', meaning: 'speech, words', components: ['言'], story: 'Mouth with lines = speech', level: 3, strokes: 7 },
  { id: 191, character: '花', pinyin: 'huā', meaning: 'flower', components: ['艹', '化'], story: 'Grass changing = flower', level: 3, strokes: 7 },
  { id: 192, character: '受', pinyin: 'shòu', meaning: 'to receive', components: ['爫', '冖', '又'], story: 'Claw cover hand = receive', level: 3, strokes: 8 },
  { id: 193, character: '作', pinyin: 'zuò', meaning: 'to do, work', components: ['亻', '乍'], story: 'Person working suddenly', level: 3, strokes: 7 },
  { id: 194, character: '建', pinyin: 'jiàn', meaning: 'to build', components: ['廴', '聿'], story: 'Walk and brush = build', level: 3, strokes: 8 },
  { id: 195, character: '活', pinyin: 'huó', meaning: 'to live, alive', components: ['氵', '舌'], story: 'Water tongue = alive', level: 3, strokes: 9 },
  { id: 196, character: '话', pinyin: 'huà', meaning: 'speech, words', components: ['讠', '舌'], story: 'Words tongue = speech', level: 3, strokes: 8 },
  { id: 197, character: '回', pinyin: 'huí', meaning: 'to return', components: ['囗', '口'], story: 'Mouth in frame = return', level: 3, strokes: 6 },
  { id: 198, character: '事', pinyin: 'shì', meaning: 'matter, affair', components: ['一', '口', '亅'], story: 'Line mouth hook = matter', level: 3, strokes: 8 },
  { id: 199, character: '起', pinyin: 'qǐ', meaning: 'to rise, start', components: ['走', '己'], story: 'Walking oneself = start', level: 3, strokes: 10 },
  { id: 200, character: '最', pinyin: 'zuì', meaning: 'most', components: ['日', '取'], story: 'Sun take = most', level: 3, strokes: 12 }
];

const HanziFlashcardApp = () => {
  // 应用状态管理
  const [currentScreen, setCurrentScreen] = useState('home');
  const [currentCard, setCurrentCard] = useState(0);
  const [showAnswer, setShowAnswer] = useState(false);
  const [userProgress, setUserProgress] = useState({});
  const [studySession, setStudySession] = useState([]);
  const [sessionStats, setSessionStats] = useState({ correct: 0, total: 0 });
  const [studyMode, setStudyMode] = useState('recognition'); // recognition, writing, listening
  const [selectedLevel, setSelectedLevel] = useState(1);
  const [streakCount, setStreakCount] = useState(0);
  const [traceMode, setTraceMode] = useState(false);
  const [favorites, setFavorites] = useState([]);
  const [dailyGoal, setDailyGoal] = useState(20);
  const [studyStreak, setStudyStreak] = useState(0);
  const [totalStudyTime, setTotalStudyTime] = useState(0);
  const [sessionStartTime, setSessionStartTime] = useState(null);
  const [showStrokeOrder, setShowStrokeOrder] = useState(false);
  const [audioEnabled, setAudioEnabled] = useState(true);
  const [darkMode, setDarkMode] = useState(false);

  const canvasRef = useRef(null);
  const [isDrawing, setIsDrawing] = useState(false);

  // 本地存储功能
  useEffect(() => {
    // 加载用户数据
    const savedProgress = localStorage.getItem('hanzi-progress');
    const savedFavorites = localStorage.getItem('hanzi-favorites');
    const savedSettings = localStorage.getItem('hanzi-settings');
    const savedStats = localStorage.getItem('hanzi-stats');

    if (savedProgress) {
      setUserProgress(JSON.parse(savedProgress));
    }
    if (savedFavorites) {
      setFavorites(JSON.parse(savedFavorites));
    }
    if (savedSettings) {
      const settings = JSON.parse(savedSettings);
      setDailyGoal(settings.dailyGoal || 20);
      setShowStrokeOrder(settings.showStrokeOrder || false);
      setAudioEnabled(settings.audioEnabled !== false);
      setDarkMode(settings.darkMode || false);
    }
    if (savedStats) {
      const stats = JSON.parse(savedStats);
      setStudyStreak(stats.studyStreak || 0);
      setTotalStudyTime(stats.totalStudyTime || 0);
    }
  }, []);

  // 保存用户数据
  useEffect(() => {
    localStorage.setItem('hanzi-progress', JSON.stringify(userProgress));
  }, [userProgress]);

  useEffect(() => {
    localStorage.setItem('hanzi-favorites', JSON.stringify(favorites));
  }, [favorites]);

  useEffect(() => {
    const settings = {
      dailyGoal,
      showStrokeOrder,
      audioEnabled,
      darkMode
    };
    localStorage.setItem('hanzi-settings', JSON.stringify(settings));
  }, [dailyGoal, showStrokeOrder, audioEnabled, darkMode]);

  useEffect(() => {
    const stats = {
      studyStreak,
      totalStudyTime
    };
    localStorage.setItem('hanzi-stats', JSON.stringify(stats));
  }, [studyStreak, totalStudyTime]);

  // 计算学习统计
  const getStudyStats = () => {
    const totalCards = Object.keys(userProgress).length;
    const masteredCards = Object.values(userProgress).filter(
      (progress: any) => progress.correct >= 3 && progress.correct / Math.max(1, progress.attempts) >= 0.8
    ).length;
    const todayStudied = Object.values(userProgress).filter(
      (progress: any) => {
        const lastSeen = new Date(progress.lastSeen);
        const today = new Date();
        return lastSeen.toDateString() === today.toDateString();
      }
    ).length;

    return { totalCards, masteredCards, todayStudied };
  };

  // 改进的间隔重复算法 (SRS)
  const calculateNextReview = (attempts: number, correct: number, lastInterval: number = 1) => {
    const accuracy = correct / Math.max(1, attempts);
    let interval = lastInterval;

    if (accuracy >= 0.9) {
      interval = Math.min(interval * 2.5, 30); // 最多30天
    } else if (accuracy >= 0.7) {
      interval = Math.min(interval * 1.5, 14); // 最多14天
    } else if (accuracy >= 0.5) {
      interval = Math.max(interval * 0.8, 1); // 最少1天
    } else {
      interval = 1; // 重新开始
    }

    return Math.round(interval);
  };

  // 初始化学习会话 - 基于改进的间隔重复算法
  const initializeSession = (level: number) => {
    setSessionStartTime(Date.now());
    const levelCards = hanziDatabase.filter(card => card.level <= level);

    // 计算每张卡片的优先级
    const prioritizedCards = levelCards.map(card => {
      const progress = userProgress[card.id] || { attempts: 0, correct: 0, lastSeen: 0, interval: 1 };
      const daysSinceLastSeen = (Date.now() - progress.lastSeen) / (24 * 60 * 60 * 1000);
      const accuracy = progress.correct / Math.max(1, progress.attempts);

      // 优先级计算：新卡片、需要复习的卡片、困难卡片
      let priority = 0;

      if (progress.attempts === 0) {
        priority = 100; // 新卡片最高优先级
      } else if (daysSinceLastSeen >= progress.interval) {
        priority = 80 + (daysSinceLastSeen - progress.interval) * 5; // 需要复习
      } else if (accuracy < 0.6) {
        priority = 60 + (0.6 - accuracy) * 50; // 困难卡片
      } else {
        priority = Math.max(0, 30 - daysSinceLastSeen * 2); // 其他卡片
      }

      return { ...card, priority, progress };
    });

    // 按优先级排序并选择前10张
    const selectedCards = prioritizedCards
      .sort((a, b) => b.priority - a.priority)
      .slice(0, 10)
      .sort(() => Math.random() - 0.5); // 随机打乱顺序

    setStudySession(selectedCards);
    setCurrentCard(0);
    setShowAnswer(false);
    setSessionStats({ correct: 0, total: 0 });
  };

  // 添加到收藏夹
  const toggleFavorite = (cardId: number) => {
    setFavorites(prev =>
      prev.includes(cardId)
        ? prev.filter(id => id !== cardId)
        : [...prev, cardId]
    );
  };

  // 语音合成
  const speakChinese = (text: string) => {
    if (audioEnabled && 'speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = 'zh-CN';
      utterance.rate = 0.8;
      speechSynthesis.speak(utterance);
    }
  };

  // 处理答案反馈 - 改进版本
  const handleAnswer = (isCorrect: boolean) => {
    const cardId = studySession[currentCard]?.id;
    if (cardId) {
      const currentProgress = userProgress[cardId] || {
        attempts: 0,
        correct: 0,
        lastSeen: 0,
        interval: 1,
        difficulty: 1
      };

      const newInterval = calculateNextReview(
        currentProgress.attempts + 1,
        currentProgress.correct + (isCorrect ? 1 : 0),
        currentProgress.interval
      );

      const newProgress = {
        attempts: currentProgress.attempts + 1,
        correct: currentProgress.correct + (isCorrect ? 1 : 0),
        lastSeen: Date.now(),
        interval: newInterval,
        difficulty: isCorrect ? Math.max(1, currentProgress.difficulty - 0.1) : currentProgress.difficulty + 0.2
      };

      setUserProgress({ ...userProgress, [cardId]: newProgress });
    }

    setSessionStats((prev: any) => ({
      correct: prev.correct + (isCorrect ? 1 : 0),
      total: prev.total + 1
    }));

    if (isCorrect) {
      setStreakCount((prev: number) => prev + 1);
    } else {
      setStreakCount(0);
    }

    // 自动前进到下一张卡片
    setTimeout(() => {
      if (currentCard < studySession.length - 1) {
        setCurrentCard(currentCard + 1);
        setShowAnswer(false);
      } else {
        // 计算学习时间
        if (sessionStartTime) {
          const sessionTime = Math.round((Date.now() - sessionStartTime) / 1000 / 60); // 分钟
          setTotalStudyTime(prev => prev + sessionTime);
        }
        setCurrentScreen('results');
      }
    }, 1500);
  };

  // 绘制功能
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!traceMode) return;
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.strokeStyle = '#4F46E5';
    ctx.lineWidth = 3;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    ctx.beginPath();
    ctx.moveTo(x, y);
    setIsDrawing(true);
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !traceMode) return;
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.lineTo(x, y);
    ctx.stroke();
  };

  const stopDrawing = () => {
    setIsDrawing(false);
  };

  const clearCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);
  };

  // 主页面组件
  const HomePage = () => {
    const stats = getStudyStats();
    const bgClass = darkMode
      ? "min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-6"
      : "min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6";

    return (
      <div className={bgClass}>
        <div className="max-w-md mx-auto">
          {/* 头部 */}
          <div className="text-center mb-8">
            <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center shadow-lg`}>
              <Brain className="w-10 h-10 text-indigo-600" />
            </div>
            <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'} mb-2`}>汉字大师</h1>
            <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Smart Chinese Character Learning</p>
          </div>

          {/* 统计卡片 */}
          <div className="grid grid-cols-2 gap-4 mb-8">
            <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-2xl p-4 shadow-sm`}>
              <div className="flex items-center justify-between">
                <div>
                  <p className={`${darkMode ? 'text-gray-400' : 'text-gray-500'} text-sm`}>今日学习</p>
                  <p className="text-2xl font-bold text-indigo-600">{stats.todayStudied}</p>
                </div>
                <Calendar className="w-8 h-8 text-indigo-400" />
              </div>
            </div>
            <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-2xl p-4 shadow-sm`}>
              <div className="flex items-center justify-between">
                <div>
                  <p className={`${darkMode ? 'text-gray-400' : 'text-gray-500'} text-sm`}>掌握汉字</p>
                  <p className="text-2xl font-bold text-green-600">{stats.masteredCards}</p>
                </div>
                <Trophy className="w-8 h-8 text-green-400" />
              </div>
            </div>
            <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-2xl p-4 shadow-sm`}>
              <div className="flex items-center justify-between">
                <div>
                  <p className={`${darkMode ? 'text-gray-400' : 'text-gray-500'} text-sm`}>连续天数</p>
                  <p className="text-2xl font-bold text-orange-600">{studyStreak}</p>
                </div>
                <Clock className="w-8 h-8 text-orange-400" />
              </div>
            </div>
            <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-2xl p-4 shadow-sm`}>
              <div className="flex items-center justify-between">
                <div>
                  <p className={`${darkMode ? 'text-gray-400' : 'text-gray-500'} text-sm`}>学习时间</p>
                  <p className="text-2xl font-bold text-purple-600">{totalStudyTime}m</p>
                </div>
                <Target className="w-8 h-8 text-purple-400" />
              </div>
            </div>
          </div>

          {/* 每日目标进度 */}
          <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-3xl p-6 mb-6 shadow-sm`}>
            <div className="flex items-center justify-between mb-4">
              <h3 className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>今日目标</h3>
              <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {stats.todayStudied}/{dailyGoal}
              </span>
            </div>
            <div className={`${darkMode ? 'bg-gray-700' : 'bg-gray-200'} rounded-full h-3 mb-2`}>
              <div
                className="bg-gradient-to-r from-indigo-500 to-purple-600 h-3 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(100, (stats.todayStudied / dailyGoal) * 100)}%` }}
              />
            </div>
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {stats.todayStudied >= dailyGoal ? '🎉 今日目标已完成！' : `还需学习 ${dailyGoal - stats.todayStudied} 个汉字`}
            </p>
          </div>
          {/* 学习模式选择 */}
          <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-3xl p-6 mb-6 shadow-sm`}>
            <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'} mb-4`}>选择学习模式</h2>

            <div className="space-y-3">
              <button
                onClick={() => {
                  setStudyMode('recognition');
                  setCurrentScreen('levelSelect');
                }}
                className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-2xl p-4 flex items-center justify-between hover:shadow-lg transition-all"
              >
                <div className="flex items-center">
                  <Eye className="w-6 h-6 mr-3" />
                  <div className="text-left">
                    <div className="font-semibold">Recognition Mode</div>
                    <div className="text-sm opacity-90">Character recognition practice</div>
                  </div>
                </div>
              </button>

              <button
                onClick={() => {
                  setStudyMode('writing');
                  setCurrentScreen('levelSelect');
                }}
                className="w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-2xl p-4 flex items-center justify-between hover:shadow-lg transition-all"
              >
                <div className="flex items-center">
                  <PenTool className="w-6 h-6 mr-3" />
                  <div className="text-left">
                    <div className="font-semibold">Writing Mode</div>
                    <div className="text-sm opacity-90">Practice writing characters</div>
                  </div>
                </div>
              </button>

              <button
                onClick={() => {
                  setStudyMode('listening');
                  setCurrentScreen('levelSelect');
                }}
                className="w-full bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-2xl p-4 flex items-center justify-between hover:shadow-lg transition-all"
              >
                <div className="flex items-center">
                  <Volume2 className="w-6 h-6 mr-3" />
                  <div className="text-left">
                    <div className="font-semibold">Listening Mode</div>
                    <div className="text-sm opacity-90">Audio-based learning</div>
                  </div>
                </div>
              </button>
            </div>
          </div>

          {/* 快速访问 */}
          <div className="grid grid-cols-3 gap-3">
            <button
              onClick={() => setCurrentScreen('progress')}
              className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-2xl p-4 flex flex-col items-center justify-center shadow-sm hover:shadow-md transition-all`}
            >
              <BarChart3 className={`w-6 h-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-1`} />
              <span className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Progress</span>
            </button>
            <button
              onClick={() => setCurrentScreen('favorites')}
              className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-2xl p-4 flex flex-col items-center justify-center shadow-sm hover:shadow-md transition-all`}
            >
              <Star className={`w-6 h-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-1`} />
              <span className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Favorites</span>
              {favorites.length > 0 && (
                <div className="w-5 h-5 bg-yellow-500 text-white text-xs rounded-full flex items-center justify-center mt-1">
                  {favorites.length}
                </div>
              )}
            </button>
            <button
              onClick={() => setCurrentScreen('settings')}
              className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-2xl p-4 flex flex-col items-center justify-center shadow-sm hover:shadow-md transition-all`}
            >
              <Settings className={`w-6 h-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-1`} />
              <span className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Settings</span>
            </button>
          </div>
        </div>
      </div>
    );
  };

  // 等级选择页面
  const LevelSelectPage = () => (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
      <div className="max-w-md mx-auto">
        <div className="flex items-center mb-6">
          <button
            onClick={() => setCurrentScreen('home')}
            className="p-2 rounded-full bg-white shadow-sm mr-4"
          >
            <RotateCcw className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-2xl font-bold text-gray-800">选择难度等级</h1>
        </div>

        <div className="space-y-4">
          {[1, 2, 3].map(level => (
            <button
              key={level}
              onClick={() => {
                setSelectedLevel(level);
                initializeSession(level);
                setCurrentScreen('study');
              }}
              className="w-full bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-all"
            >
              <div className="flex items-center justify-between">
                <div className="text-left">
                  <div className="text-xl font-bold text-gray-800">HSK Level {level}</div>
                  <div className="text-gray-600 text-sm">
                    {level === 1 && "Basic characters (150 characters)"}
                    {level === 2 && "Elementary level (300 characters)"}
                    {level === 3 && "Intermediate level (600 characters)"}
                  </div>
                  <div className="mt-2">
                    <div className="flex items-center text-sm text-green-600">
                      <Star className="w-4 h-4 mr-1" />
                      Mastered: {hanziDatabase.filter(card =>
                        card.level <= level &&
                        userProgress[card.id]?.correct > 2
                      ).length}
                    </div>
                  </div>
                </div>
                <Target className="w-8 h-8 text-indigo-400" />
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );

  // 学习页面
  const StudyPage = () => {
    if (studySession.length === 0) return null;

    const card = studySession[currentCard];
    if (!card) return null;

    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
        <div className="max-w-md mx-auto">
          {/* 头部进度 */}
          <div className="flex items-center justify-between mb-6">
            <button
              onClick={() => setCurrentScreen('home')}
              className="p-2 rounded-full bg-white shadow-sm"
            >
              <RotateCcw className="w-6 h-6 text-gray-600" />
            </button>
            <div className="flex-1 mx-4">
              <div className="bg-white rounded-full h-2 overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-indigo-500 to-purple-600 transition-all duration-300"
                  style={{ width: `${((currentCard + 1) / studySession.length) * 100}%` }}
                />
              </div>
              <div className="text-center text-sm text-gray-600 mt-1">
                {currentCard + 1} / {studySession.length}
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-600">Streak</div>
              <div className="text-lg font-bold text-orange-600">{streakCount}</div>
            </div>
          </div>

          {/* 汉字卡片 */}
          <div className="bg-white rounded-3xl p-8 mb-6 shadow-lg text-center min-h-[400px] flex flex-col justify-center">
            {studyMode === 'recognition' && (
              <>
                <div className="text-8xl font-bold text-gray-800 mb-6">{card.character}</div>
                <button
                  onClick={() => speakChinese(card.character)}
                  className="mx-auto mb-4 p-3 bg-indigo-100 rounded-full hover:bg-indigo-200 transition-colors"
                >
                  <Volume2 className="w-6 h-6 text-indigo-600" />
                </button>

                {showAnswer && (
                  <div className="space-y-4 animate-fade-in">
                    <div className="flex items-center justify-center space-x-4">
                      <div className="text-2xl font-bold text-indigo-600">{card.pinyin}</div>
                      <button
                        onClick={() => toggleFavorite(card.id)}
                        className={`p-2 rounded-full transition-colors ${
                          favorites.includes(card.id)
                            ? 'bg-yellow-100 text-yellow-600'
                            : 'bg-gray-100 text-gray-400 hover:text-yellow-600'
                        }`}
                      >
                        <Star className="w-5 h-5" fill={favorites.includes(card.id) ? 'currentColor' : 'none'} />
                      </button>
                    </div>
                    <div className="text-xl text-gray-700">{card.meaning}</div>
                    <div className="bg-blue-50 rounded-2xl p-4">
                      <div className="text-sm text-gray-600 mb-2">Memory Story:</div>
                      <div className="text-gray-800">{card.story}</div>
                    </div>
                    <div className="text-sm text-gray-500">
                      Strokes: {card.strokes} | Components: {card.components.join(', ')}
                    </div>
                  </div>
                )}
              </>
            )}

            {studyMode === 'writing' && (
              <>
                <div className="text-xl text-gray-700 mb-4">{card.meaning}</div>
                <div className="text-lg text-indigo-600 mb-6">{card.pinyin}</div>

                <div className="relative mb-6">
                  <canvas
                    ref={canvasRef}
                    width={200}
                    height={200}
                    className="border-2 border-dashed border-gray-300 rounded-2xl mx-auto bg-gray-50"
                    onMouseDown={startDrawing}
                    onMouseMove={draw}
                    onMouseUp={stopDrawing}
                    onMouseLeave={stopDrawing}
                  />
                  <div className="absolute inset-0 text-9xl text-gray-200 flex items-center justify-center pointer-events-none">
                    {traceMode ? card.character : ''}
                  </div>
                </div>

                <div className="flex space-x-4 justify-center mb-6">
                  <button
                    onClick={() => setTraceMode(!traceMode)}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                      traceMode ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-700'
                    }`}
                  >
                    Trace Mode
                  </button>
                  <button
                    onClick={clearCanvas}
                    className="px-4 py-2 bg-gray-200 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-300 transition-colors"
                  >
                    Clear
                  </button>
                </div>

                {showAnswer && (
                  <div className="animate-fade-in">
                    <div className="text-6xl font-bold text-gray-800 mb-4">{card.character}</div>
                    <div className="bg-blue-50 rounded-2xl p-4">
                      <div className="text-sm text-gray-600 mb-2">Stroke Order & Story:</div>
                      <div className="text-gray-800">{card.story}</div>
                    </div>
                  </div>
                )}
              </>
            )}

            {studyMode === 'listening' && (
              <>
                <div className="mb-8">
                  <button
                    onClick={() => speakChinese(card.character)}
                    className="mx-auto p-6 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full text-white hover:shadow-lg transition-all"
                  >
                    <Volume2 className="w-12 h-12" />
                  </button>
                  <div className="text-gray-600 mt-4">Listen and choose the correct character</div>
                </div>

                {showAnswer && (
                  <div className="space-y-4 animate-fade-in">
                    <div className="text-8xl font-bold text-gray-800">{card.character}</div>
                    <div className="text-2xl font-bold text-indigo-600">{card.pinyin}</div>
                    <div className="text-xl text-gray-700">{card.meaning}</div>
                  </div>
                )}
              </>
            )}
          </div>

          {/* 控制按钮 */}
          {!showAnswer ? (
            <button
              onClick={() => setShowAnswer(true)}
              className="w-full bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-2xl py-4 font-semibold text-lg hover:shadow-lg transition-all"
            >
              Show Answer
            </button>
          ) : (
            <div className="flex space-x-4">
              <button
                onClick={() => handleAnswer(false)}
                className="flex-1 bg-gradient-to-r from-red-500 to-pink-600 text-white rounded-2xl py-4 flex items-center justify-center hover:shadow-lg transition-all"
              >
                <XCircle className="w-6 h-6 mr-2" />
                Hard
              </button>
              <button
                onClick={() => handleAnswer(true)}
                className="flex-1 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-2xl py-4 flex items-center justify-center hover:shadow-lg transition-all"
              >
                <CheckCircle className="w-6 h-6 mr-2" />
                Easy
              </button>
            </div>
          )}
        </div>
      </div>
    );
  };

  // 结果页面
  const ResultsPage = () => (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
      <div className="max-w-md mx-auto text-center">
        <div className="bg-white rounded-3xl p-8 mb-6 shadow-lg">
          <div className="text-6xl mb-4">🎉</div>
          <h2 className="text-3xl font-bold text-gray-800 mb-4">Session Complete!</h2>

          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-green-50 rounded-2xl p-4">
              <div className="text-2xl font-bold text-green-600">{sessionStats.correct}</div>
              <div className="text-green-700 text-sm">Correct</div>
            </div>
            <div className="bg-blue-50 rounded-2xl p-4">
              <div className="text-2xl font-bold text-blue-600">{sessionStats.total}</div>
              <div className="text-blue-700 text-sm">Total</div>
            </div>
          </div>

          <div className="text-lg text-gray-700 mb-6">
            Accuracy: {Math.round((sessionStats.correct / sessionStats.total) * 100)}%
          </div>

          <div className="space-y-3">
            <button
              onClick={() => {
                initializeSession(selectedLevel);
                setCurrentScreen('study');
              }}
              className="w-full bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-2xl py-3 font-semibold hover:shadow-lg transition-all"
            >
              Study Again
            </button>
            <button
              onClick={() => setCurrentScreen('home')}
              className="w-full bg-gray-200 text-gray-700 rounded-2xl py-3 font-semibold hover:bg-gray-300 transition-all"
            >
              Back to Home
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  // 进度页面
  const ProgressPage = () => {
    const bgClass = darkMode
      ? "min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-6"
      : "min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6";

    return (
      <div className={bgClass}>
        <div className="max-w-md mx-auto">
          <div className="flex items-center mb-6">
            <button
              onClick={() => setCurrentScreen('home')}
              className={`p-2 rounded-full ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm mr-4`}
            >
              <RotateCcw className={`w-6 h-6 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`} />
            </button>
            <h1 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>Learning Progress</h1>
          </div>

          <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-3xl p-6 shadow-lg`}>
            <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'} mb-4`}>Your Statistics</h3>

            {[1, 2, 3].map(level => {
              const levelCards = hanziDatabase.filter(card => card.level === level);
              const masteredCards = levelCards.filter(card =>
                userProgress[card.id]?.correct > 2
              );

              return (
                <div key={level} className="mb-6">
                  <div className="flex justify-between items-center mb-2">
                    <span className={`font-semibold ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>HSK Level {level}</span>
                    <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      {masteredCards.length}/{levelCards.length}
                    </span>
                  </div>
                  <div className={`${darkMode ? 'bg-gray-700' : 'bg-gray-200'} rounded-full h-3`}>
                    <div
                      className="bg-gradient-to-r from-green-400 to-green-600 h-3 rounded-full transition-all duration-300"
                      style={{ width: `${(masteredCards.length / levelCards.length) * 100}%` }}
                    />
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  // 收藏夹页面
  const FavoritesPage = () => {
    const favoriteCards = hanziDatabase.filter(card => favorites.includes(card.id));
    const bgClass = darkMode
      ? "min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-6"
      : "min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6";

    return (
      <div className={bgClass}>
        <div className="max-w-md mx-auto">
          <div className="flex items-center mb-6">
            <button
              onClick={() => setCurrentScreen('home')}
              className={`p-2 rounded-full ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm mr-4`}
            >
              <RotateCcw className={`w-6 h-6 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`} />
            </button>
            <h1 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>Favorites</h1>
          </div>

          {favoriteCards.length === 0 ? (
            <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-3xl p-8 shadow-lg text-center`}>
              <Star className={`w-16 h-16 ${darkMode ? 'text-gray-600' : 'text-gray-300'} mx-auto mb-4`} />
              <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'} mb-2`}>No Favorites Yet</h3>
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Start learning and add characters to your favorites by tapping the star icon!
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {favoriteCards.map(card => (
                <div key={card.id} className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-2xl p-4 shadow-sm`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="text-4xl font-bold text-gray-800">{card.character}</div>
                      <div>
                        <div className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-800'}`}>{card.pinyin}</div>
                        <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{card.meaning}</div>
                        <div className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-500'}`}>
                          HSK {card.level} • {card.strokes} strokes
                        </div>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => speakChinese(card.character)}
                        className={`p-2 rounded-full ${darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'} hover:bg-indigo-100 hover:text-indigo-600 transition-colors`}
                      >
                        <Volume2 className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => toggleFavorite(card.id)}
                        className="p-2 rounded-full bg-yellow-100 text-yellow-600 hover:bg-yellow-200 transition-colors"
                      >
                        <Star className="w-4 h-4" fill="currentColor" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}

              {favoriteCards.length > 0 && (
                <button
                  onClick={() => {
                    // 创建收藏夹学习会话
                    setStudySession(favoriteCards.sort(() => Math.random() - 0.5).slice(0, 10));
                    setCurrentCard(0);
                    setShowAnswer(false);
                    setSessionStats({ correct: 0, total: 0 });
                    setSessionStartTime(Date.now());
                    setCurrentScreen('study');
                  }}
                  className="w-full bg-gradient-to-r from-yellow-500 to-orange-600 text-white rounded-2xl py-4 font-semibold text-lg hover:shadow-lg transition-all"
                >
                  Study Favorites
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  // 设置页面
  const SettingsPage = () => {
    const bgClass = darkMode
      ? "min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-6"
      : "min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6";

    return (
      <div className={bgClass}>
        <div className="max-w-md mx-auto">
          <div className="flex items-center mb-6">
            <button
              onClick={() => setCurrentScreen('home')}
              className={`p-2 rounded-full ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm mr-4`}
            >
              <RotateCcw className={`w-6 h-6 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`} />
            </button>
            <h1 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>Settings</h1>
          </div>

          <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-3xl p-6 shadow-lg mb-6`}>
            <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'} mb-4`}>Learning Preferences</h3>

            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Audio Pronunciation</span>
                <button
                  onClick={() => setAudioEnabled(!audioEnabled)}
                  className={`w-12 h-6 rounded-full flex items-center px-1 transition-colors ${
                    audioEnabled ? 'bg-indigo-600' : 'bg-gray-300'
                  }`}
                >
                  <div className={`w-4 h-4 bg-white rounded-full transition-transform ${
                    audioEnabled ? 'translate-x-6' : 'translate-x-0'
                  }`}></div>
                </button>
              </div>

              <div className="flex justify-between items-center">
                <span className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Show Stroke Order</span>
                <button
                  onClick={() => setShowStrokeOrder(!showStrokeOrder)}
                  className={`w-12 h-6 rounded-full flex items-center px-1 transition-colors ${
                    showStrokeOrder ? 'bg-indigo-600' : 'bg-gray-300'
                  }`}
                >
                  <div className={`w-4 h-4 bg-white rounded-full transition-transform ${
                    showStrokeOrder ? 'translate-x-6' : 'translate-x-0'
                  }`}></div>
                </button>
              </div>

              <div className="flex justify-between items-center">
                <span className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Dark Mode</span>
                <button
                  onClick={() => setDarkMode(!darkMode)}
                  className={`w-12 h-6 rounded-full flex items-center px-1 transition-colors ${
                    darkMode ? 'bg-indigo-600' : 'bg-gray-300'
                  }`}
                >
                  <div className={`w-4 h-4 bg-white rounded-full transition-transform ${
                    darkMode ? 'translate-x-6' : 'translate-x-0'
                  }`}></div>
                </button>
              </div>
            </div>
          </div>

          {/* 每日目标设置 */}
          <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-3xl p-6 shadow-lg mb-6`}>
            <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'} mb-4`}>Daily Goal</h3>
            <div className="flex items-center space-x-4">
              <span className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Characters per day:</span>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setDailyGoal(Math.max(5, dailyGoal - 5))}
                  className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-300"
                >
                  -
                </button>
                <span className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-800'} min-w-[3rem] text-center`}>
                  {dailyGoal}
                </span>
                <button
                  onClick={() => setDailyGoal(Math.min(100, dailyGoal + 5))}
                  className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-300"
                >
                  +
                </button>
              </div>
            </div>
          </div>

          {/* 数据管理 */}
          <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-3xl p-6 shadow-lg`}>
            <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'} mb-4`}>Data Management</h3>
            <div className="space-y-3">
              <button
                onClick={() => {
                  if (confirm('Are you sure you want to reset all progress? This cannot be undone.')) {
                    setUserProgress({});
                    setFavorites([]);
                    setStudyStreak(0);
                    setTotalStudyTime(0);
                  }
                }}
                className="w-full bg-red-500 text-white rounded-2xl py-3 font-semibold hover:bg-red-600 transition-colors"
              >
                Reset All Progress
              </button>
              <button
                onClick={() => {
                  const data = {
                    userProgress,
                    favorites,
                    settings: { dailyGoal, showStrokeOrder, audioEnabled, darkMode },
                    stats: { studyStreak, totalStudyTime }
                  };
                  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = 'hanzi-flashcard-backup.json';
                  a.click();
                  URL.revokeObjectURL(url);
                }}
                className={`w-full ${darkMode ? 'bg-gray-700 text-gray-200' : 'bg-gray-200 text-gray-700'} rounded-2xl py-3 font-semibold hover:bg-gray-300 transition-colors`}
              >
                Export Data
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // 渲染当前屏幕
  const renderCurrentScreen = () => {
    switch (currentScreen) {
      case 'home':
        return <HomePage />;
      case 'levelSelect':
        return <LevelSelectPage />;
      case 'study':
        return <StudyPage />;
      case 'results':
        return <ResultsPage />;
      case 'progress':
        return <ProgressPage />;
      case 'favorites':
        return <FavoritesPage />;
      case 'settings':
        return <SettingsPage />;
      default:
        return <HomePage />;
    }
  };

  return (
    <div className="font-sans">
      {renderCurrentScreen()}
      <style jsx>{`
        @keyframes fade-in {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fade-in {
          animation: fade-in 0.3s ease-out;
        }
      `}</style>
    </div>
  );
};

export default HanziFlashcardApp;