import React, { useState, useEffect, useRef } from 'react';
import { 
  BookOpen, 
  Brain, 
  RotateCcw, 
  CheckCircle, 
  XCircle, 
  Star,
  Play,
  Pause,
  Volume2,
  Eye,
  PenTool,
  BarChart3,
  Settings,
  Home,
  Trophy,
  Clock,
  Target,
  Calendar
} from 'lucide-react';

// 汉字数据库 - 基于HSK等级和认知科学原理排序 (800个高频汉字)
const hanziDatabase = [
  // HSK 1级 - 基础高频字 (150字)
  { id: 1, character: '的', pinyin: 'de', meaning: 'possessive particle', components: ['白', '勺'], story: 'White spoon shows possession', level: 1, strokes: 8 },
  { id: 2, character: '一', pinyin: 'yī', meaning: 'one', components: ['一'], story: 'Single horizontal line', level: 1, strokes: 1 },
  { id: 3, character: '是', pinyin: 'shì', meaning: 'to be', components: ['日', '正'], story: 'Sun above correct = is', level: 1, strokes: 9 },
  { id: 4, character: '不', pinyin: 'bù', meaning: 'not', components: ['不'], story: 'Bird cannot fly upward', level: 1, strokes: 4 },
  { id: 5, character: '了', pinyin: 'le', meaning: 'completed action', components: ['了'], story: 'Person completed a task', level: 1, strokes: 2 },
  { id: 6, character: '人', pinyin: 'rén', meaning: 'person, people', components: ['人'], story: 'Looks like a person walking', level: 1, strokes: 2 },
  { id: 7, character: '我', pinyin: 'wǒ', meaning: 'I, me', components: ['戈', '扌'], story: 'Hand holding weapon = I', level: 1, strokes: 7 },
  { id: 8, character: '在', pinyin: 'zài', meaning: 'at, in', components: ['土', '才'], story: 'Talent on earth = located at', level: 1, strokes: 6 },
  { id: 9, character: '有', pinyin: 'yǒu', meaning: 'to have', components: ['月', '又'], story: 'Moon and hand = to have', level: 1, strokes: 6 },
  { id: 10, character: '他', pinyin: 'tā', meaning: 'he, him', components: ['亻', '也'], story: 'Person + also = he too', level: 1, strokes: 5 },
  { id: 11, character: '这', pinyin: 'zhè', meaning: 'this', components: ['辶', '文'], story: 'Walking to text = this', level: 1, strokes: 7 },
  { id: 12, character: '中', pinyin: 'zhōng', meaning: 'middle, center', components: ['中'], story: 'Arrow through center of target', level: 1, strokes: 4 },
  { id: 13, character: '大', pinyin: 'dà', meaning: 'big, large', components: ['大'], story: 'A person spreading arms wide', level: 1, strokes: 3 },
  { id: 14, character: '来', pinyin: 'lái', meaning: 'to come', components: ['来'], story: 'Wheat coming from earth', level: 1, strokes: 7 },
  { id: 15, character: '上', pinyin: 'shàng', meaning: 'up, above', components: ['上'], story: 'Line above the base', level: 1, strokes: 3 },
  { id: 16, character: '国', pinyin: 'guó', meaning: 'country, nation', components: ['囗', '玉'], story: 'Jade treasure within borders', level: 1, strokes: 8 },
  { id: 17, character: '个', pinyin: 'gè', meaning: 'individual, classifier', components: ['人', '丶'], story: 'Person with a dot', level: 1, strokes: 3 },
  { id: 18, character: '到', pinyin: 'dào', meaning: 'to arrive', components: ['至', '刂'], story: 'Reach with a knife = arrive', level: 1, strokes: 8 },
  { id: 19, character: '说', pinyin: 'shuō', meaning: 'to speak', components: ['讠', '兑'], story: 'Words + exchange = speak', level: 1, strokes: 9 },
  { id: 20, character: '们', pinyin: 'men', meaning: 'plural marker', components: ['亻', '门'], story: 'Person at door = plural', level: 1, strokes: 5 },
  { id: 21, character: '为', pinyin: 'wéi', meaning: 'for, because of', components: ['为'], story: 'Elephant working for others', level: 1, strokes: 4 },
  { id: 22, character: '子', pinyin: 'zǐ', meaning: 'child, son', components: ['子'], story: 'Baby with arms outstretched', level: 1, strokes: 3 },
  { id: 23, character: '和', pinyin: 'hé', meaning: 'and, with', components: ['禾', '口'], story: 'Grain and mouth = harmony', level: 1, strokes: 8 },
  { id: 24, character: '你', pinyin: 'nǐ', meaning: 'you', components: ['亻', '尔'], story: 'Person that is you', level: 1, strokes: 7 },
  { id: 25, character: '地', pinyin: 'dì', meaning: 'earth, ground', components: ['土', '也'], story: 'Soil that is earth', level: 1, strokes: 6 },
  { id: 26, character: '出', pinyin: 'chū', meaning: 'to go out', components: ['凵', '山'], story: 'Mountain coming out of valley', level: 1, strokes: 5 },
  { id: 27, character: '道', pinyin: 'dào', meaning: 'road, way', components: ['辶', '首'], story: 'Walking on the first path', level: 1, strokes: 12 },
  { id: 28, character: '也', pinyin: 'yě', meaning: 'also, too', components: ['也'], story: 'Snake slithering also', level: 1, strokes: 3 },
  { id: 29, character: '时', pinyin: 'shí', meaning: 'time', components: ['日', '寺'], story: 'Sun at temple = time', level: 1, strokes: 7 },
  { id: 30, character: '年', pinyin: 'nián', meaning: 'year', components: ['年'], story: 'Person carrying grain = harvest year', level: 1, strokes: 6 },
  { id: 31, character: '得', pinyin: 'de', meaning: 'to get, obtain', components: ['彳', '旦', '寸'], story: 'Step at dawn with hand = obtain', level: 1, strokes: 11 },
  { id: 32, character: '就', pinyin: 'jiù', meaning: 'then, immediately', components: ['京', '尤'], story: 'Capital especially = then', level: 1, strokes: 12 },
  { id: 33, character: '那', pinyin: 'nà', meaning: 'that', components: ['冄', '阝'], story: 'Two people by hill = that', level: 1, strokes: 6 },
  { id: 34, character: '要', pinyin: 'yào', meaning: 'to want', components: ['西', '女'], story: 'Woman wanting west', level: 1, strokes: 9 },
  { id: 35, character: '下', pinyin: 'xià', meaning: 'down, below', components: ['下'], story: 'Line below the base', level: 1, strokes: 3 },
  { id: 36, character: '以', pinyin: 'yǐ', meaning: 'with, by means of', components: ['以'], story: 'Person using tool', level: 1, strokes: 4 },
  { id: 37, character: '生', pinyin: 'shēng', meaning: 'to be born, life', components: ['生'], story: 'Plant growing from earth', level: 1, strokes: 5 },
  { id: 38, character: '会', pinyin: 'huì', meaning: 'can, meeting', components: ['人', '云'], story: 'People under clouds meeting', level: 1, strokes: 6 },
  { id: 39, character: '家', pinyin: 'jiā', meaning: 'home, family', components: ['宀', '豕'], story: 'Pig under roof means home', level: 1, strokes: 10 },
  { id: 40, character: '可', pinyin: 'kě', meaning: 'can, may', components: ['丁', '口'], story: 'Nail in mouth = can do', level: 1, strokes: 5 },
  { id: 41, character: '她', pinyin: 'tā', meaning: 'she, her', components: ['女', '也'], story: 'Woman that is she', level: 1, strokes: 6 },
  { id: 42, character: '里', pinyin: 'lǐ', meaning: 'inside, village', components: ['田', '土'], story: 'Fields and earth = village', level: 1, strokes: 7 },
  { id: 43, character: '后', pinyin: 'hòu', meaning: 'after, behind', components: ['彳', '口'], story: 'Step and mouth = behind', level: 1, strokes: 6 },
  { id: 44, character: '小', pinyin: 'xiǎo', meaning: 'small, little', components: ['小'], story: 'Three small dots getting smaller', level: 1, strokes: 3 },
  { id: 45, character: '么', pinyin: 'me', meaning: 'what, question particle', components: ['幺'], story: 'Tiny thread = what?', level: 1, strokes: 3 },
  { id: 46, character: '心', pinyin: 'xīn', meaning: 'heart, mind', components: ['心'], story: 'Heart with chambers', level: 1, strokes: 4 },
  { id: 47, character: '多', pinyin: 'duō', meaning: 'many, much', components: ['夕', '夕'], story: 'Two evenings = many', level: 1, strokes: 6 },
  { id: 48, character: '天', pinyin: 'tiān', meaning: 'day, sky', components: ['一', '大'], story: 'Big line above = sky', level: 1, strokes: 4 },
  { id: 49, character: '而', pinyin: 'ér', meaning: 'and, but', components: ['而'], story: 'Whiskers and = but', level: 1, strokes: 6 },
  { id: 50, character: '能', pinyin: 'néng', meaning: 'can, able', components: ['月', '匕', '匕'], story: 'Moon and two spoons = able', level: 1, strokes: 10 },
  
  // 继续HSK 1级
  { id: 51, character: '好', pinyin: 'hǎo', meaning: 'good, well', components: ['女', '子'], story: 'Woman and child = good', level: 1, strokes: 6 },
  { id: 52, character: '都', pinyin: 'dōu', meaning: 'all, both', components: ['者', '阝'], story: 'Person by city = all', level: 1, strokes: 10 },
  { id: 53, character: '然', pinyin: 'rán', meaning: 'so, like that', components: ['月', '犬', '灬'], story: 'Moon, dog, fire = natural', level: 1, strokes: 12 },
  { id: 54, character: '没', pinyin: 'méi', meaning: 'not have', components: ['氵', '殳'], story: 'Water without weapon = nothing', level: 1, strokes: 7 },
  { id: 55, character: '日', pinyin: 'rì', meaning: 'day, sun', components: ['日'], story: 'Square sun with center dot', level: 1, strokes: 4 },
  { id: 56, character: '对', pinyin: 'duì', meaning: 'correct, pair', components: ['又', '寸'], story: 'Hand and inch = correct', level: 1, strokes: 5 },
  { id: 57, character: '起', pinyin: 'qǐ', meaning: 'to rise, start', components: ['走', '己'], story: 'Walking oneself = to start', level: 1, strokes: 10 },
  { id: 58, character: '还', pinyin: 'hái', meaning: 'still, yet', components: ['辶', '不'], story: 'Walking not = still going', level: 1, strokes: 7 },
  { id: 59, character: '发', pinyin: 'fā', meaning: 'to send, emit', components: ['癶', '友'], story: 'Footsteps of friend = send', level: 1, strokes: 5 },
  { id: 60, character: '成', pinyin: 'chéng', meaning: 'to become', components: ['戊', '丁'], story: 'Weapon and nail = accomplish', level: 1, strokes: 6 },
  { id: 61, character: '事', pinyin: 'shì', meaning: 'matter, thing', components: ['一', '口', '亅'], story: 'Line, mouth, hook = matter', level: 1, strokes: 8 },
  { id: 62, character: '只', pinyin: 'zhǐ', meaning: 'only, just', components: ['口', '八'], story: 'Mouth and eight = only', level: 1, strokes: 5 },
  { id: 63, character: '作', pinyin: 'zuò', meaning: 'to do, make', components: ['亻', '乍'], story: 'Person working suddenly = to do', level: 1, strokes: 7 },
  { id: 64, character: '当', pinyin: 'dāng', meaning: 'when, should', components: ['彐', '田'], story: 'Snout over field = when', level: 1, strokes: 6 },
  { id: 65, character: '想', pinyin: 'xiǎng', meaning: 'to think', components: ['相', '心'], story: 'Face and heart = to think', level: 1, strokes: 13 },
  { id: 66, character: '看', pinyin: 'kàn', meaning: 'to see, look', components: ['手', '目'], story: 'Hand over eye = to look', level: 1, strokes: 9 },
  { id: 67, character: '文', pinyin: 'wén', meaning: 'text, culture', components: ['文'], story: 'Crossed lines = writing', level: 1, strokes: 4 },
  { id: 68, character: '无', pinyin: 'wú', meaning: 'without, none', components: ['无'], story: 'Person dancing = nothing', level: 1, strokes: 4 },
  { id: 69, character: '开', pinyin: 'kāi', meaning: 'to open', components: ['廾', '一'], story: 'Two hands lifting bar = open', level: 1, strokes: 4 },
  { id: 70, character: '手', pinyin: 'shǒu', meaning: 'hand', components: ['手'], story: 'Palm and fingers', level: 1, strokes: 4 },
  { id: 71, character: '十', pinyin: 'shí', meaning: 'ten', components: ['十'], story: 'Cross shape = ten', level: 1, strokes: 2 },
  { id: 72, character: '用', pinyin: 'yòng', meaning: 'to use', components: ['用'], story: 'Bucket for use', level: 1, strokes: 5 },
  { id: 73, character: '主', pinyin: 'zhǔ', meaning: 'main, master', components: ['丶', '王'], story: 'Dot over king = master', level: 1, strokes: 5 },
  { id: 74, character: '行', pinyin: 'xíng', meaning: 'to walk, ok', components: ['彳', '亍'], story: 'Left and right step = walk', level: 1, strokes: 6 },
  { id: 75, character: '方', pinyin: 'fāng', meaning: 'square, direction', components: ['方'], story: 'Square with dot = direction', level: 1, strokes: 4 },
  { id: 76, character: '又', pinyin: 'yòu', meaning: 'again, also', components: ['又'], story: 'Right hand = again', level: 1, strokes: 2 },
  { id: 77, character: '如', pinyin: 'rú', meaning: 'like, as', components: ['女', '口'], story: 'Woman\'s mouth = like', level: 1, strokes: 6 },
  { id: 78, character: '前', pinyin: 'qián', meaning: 'front, before', components: ['刖', '刂'], story: 'Foot cut off = front', level: 1, strokes: 9 },
  { id: 79, character: '所', pinyin: 'suǒ', meaning: 'place, that which', components: ['户', '斤'], story: 'Door and axe = place', level: 1, strokes: 8 },
  { id: 80, character: '本', pinyin: 'běn', meaning: 'book, origin', components: ['木', '一'], story: 'Tree with line = root/book', level: 1, strokes: 5 },
  { id: 81, character: '见', pinyin: 'jiàn', meaning: 'to see', components: ['目', '儿'], story: 'Eye with legs = to see', level: 1, strokes: 4 },
  { id: 82, character: '经', pinyin: 'jīng', meaning: 'through, classic', components: ['纟', '圣'], story: 'Thread and sage = classic', level: 1, strokes: 8 },
  { id: 83, character: '头', pinyin: 'tóu', meaning: 'head', components: ['大', '丶'], story: 'Big with dot = head', level: 1, strokes: 5 },
  { id: 84, character: '面', pinyin: 'miàn', meaning: 'face, surface', components: ['面'], story: 'Square face outline', level: 1, strokes: 9 },
  { id: 85, character: '公', pinyin: 'gōng', meaning: 'public, male', components: ['八', '厶'], story: 'Eight and private = public', level: 1, strokes: 4 },
  { id: 86, character: '同', pinyin: 'tóng', meaning: 'same, together', components: ['冂', '一', '口'], story: 'Frame with line and mouth = same', level: 1, strokes: 6 },
  { id: 87, character: '三', pinyin: 'sān', meaning: 'three', components: ['三'], story: 'Three horizontal lines', level: 1, strokes: 3 },
  { id: 88, character: '已', pinyin: 'yǐ', meaning: 'already', components: ['已'], story: 'Snake coiled = already done', level: 1, strokes: 3 },
  { id: 89, character: '老', pinyin: 'lǎo', meaning: 'old', components: ['老'], story: 'Person with bent back = old', level: 1, strokes: 6 },
  { id: 90, character: '从', pinyin: 'cóng', meaning: 'from, follow', components: ['人', '人'], story: 'Person following person', level: 1, strokes: 4 },
  { id: 91, character: '动', pinyin: 'dòng', meaning: 'to move', components: ['云', '力'], story: 'Cloud with force = movement', level: 1, strokes: 6 },
  { id: 92, character: '两', pinyin: 'liǎng', meaning: 'two, both', components: ['一', '冂', '山'], story: 'One frame two mountains = two', level: 1, strokes: 7 },
  { id: 93, character: '长', pinyin: 'cháng', meaning: 'long', components: ['长'], story: 'Long hair flowing = long', level: 1, strokes: 4 },
  { id: 94, character: '回', pinyin: 'huí', meaning: 'to return', components: ['囗', '口'], story: 'Mouth within frame = return', level: 1, strokes: 6 },
  { id: 95, character: '什', pinyin: 'shén', meaning: 'what', components: ['亻', '十'], story: 'Person and ten = what', level: 1, strokes: 4 },
  { id: 96, character: '二', pinyin: 'èr', meaning: 'two', components: ['二'], story: 'Two horizontal lines', level: 1, strokes: 2 },
  { id: 97, character: '水', pinyin: 'shuǐ', meaning: 'water', components: ['水'], story: 'Flowing water with drops', level: 1, strokes: 4 },
  { id: 98, character: '新', pinyin: 'xīn', meaning: 'new', components: ['亲', '斤'], story: 'Close with axe = new cut', level: 1, strokes: 13 },
  { id: 99, character: '手', pinyin: 'shǒu', meaning: 'hand', components: ['手'], story: 'Palm with fingers', level: 1, strokes: 4 },
  { id: 100, character: '高', pinyin: 'gāo', meaning: 'tall, high', components: ['亠', '口', '冂', '口'], story: 'Tower with doors = high', level: 1, strokes: 10 },
  
  // HSK 2级开始 (150字)
  { id: 101, character: '学', pinyin: 'xué', meaning: 'study, learn', components: ['学'], story: 'Child under roof learning', level: 2, strokes: 8 },
  { id: 102, character: '自', pinyin: 'zì', meaning: 'self, from', components: ['自'], story: 'Nose pointing to self', level: 2, strokes: 6 },
  { id: 103, character: '分', pinyin: 'fēn', meaning: 'divide, minute', components: ['八', '刀'], story: 'Eight divided by knife', level: 2, strokes: 4 },
  { id: 104, character: '总', pinyin: 'zǒng', meaning: 'total, always', components: ['悤', '心'], story: 'Hurried heart = always total', level: 2, strokes: 9 },
  { id: 105, character: '给', pinyin: 'gěi', meaning: 'to give', components: ['纟', '合'], story: 'Thread coming together = give', level: 2, strokes: 9 },
  { id: 106, character: '身', pinyin: 'shēn', meaning: 'body', components: ['身'], story: 'Pregnant person = body', level: 2, strokes: 7 },
  { id: 107, character: '此', pinyin: 'cǐ', meaning: 'this, here', components: ['止', '匕'], story: 'Stop and spoon = this', level: 2, strokes: 6 },
  { id: 108, character: '其', pinyin: 'qí', meaning: 'his, her, its', components: ['其'], story: 'Basket woven = its', level: 2, strokes: 8 },
  { id: 109, character: '安', pinyin: 'ān', meaning: 'safe, peaceful', components: ['宀', '女'], story: 'Woman under roof = safe', level: 2, strokes: 6 },
  { id: 110, character: '今', pinyin: 'jīn', meaning: 'now, today', components: ['人', '一'], story: 'Person over line = now', level: 2, strokes: 4 },
  { id: 111, character: '次', pinyin: 'cì', meaning: 'time, order', components: ['冫', '欠'], story: 'Ice and yawn = next time', level: 2, strokes: 6 },
  { id: 112, character: '使', pinyin: 'shǐ', meaning: 'to use, make', components: ['亻', '吏'], story: 'Person as official = to use', level: 2, strokes: 8 },
  { id: 113, character: '间', pinyin: 'jiān', meaning: 'between, room', components: ['门', '日'], story: 'Sun through door = between', level: 2, strokes: 7 },
  { id: 114, character: '理', pinyin: 'lǐ', meaning: 'reason, logic', components: ['王', '里'], story: 'King in village = reason', level: 2, strokes: 11 },
  { id: 115, character: '明', pinyin: 'míng', meaning: 'bright, clear', components: ['日', '月'], story: 'Sun and moon = bright', level: 2, strokes: 8 },
  { id: 116, character: '性', pinyin: 'xìng', meaning: 'nature, character', components: ['忄', '生'], story: 'Heart and birth = nature', level: 2, strokes: 8 },
  { id: 117, character: '知', pinyin: 'zhī', meaning: 'to know', components: ['矢', '口'], story: 'Arrow to mouth = to know', level: 2, strokes: 8 },
  { id: 118, character: '国', pinyin: 'guó', meaning: 'country', components: ['囗', '玉'], story: 'Jade within borders', level: 2, strokes: 8 },
  { id: 119, character: '意', pinyin: 'yì', meaning: 'meaning, intention', components: ['立', '日', '心'], story: 'Stand, sun, heart = meaning', level: 2, strokes: 13 },
  {

const HanziFlashcardApp = () => {
  // 应用状态管理
  const [currentScreen, setCurrentScreen] = useState('home');
  const [currentCard, setCurrentCard] = useState(0);
  const [showAnswer, setShowAnswer] = useState(false);
  const [userProgress, setUserProgress] = useState({});
  const [studySession, setStudySession] = useState([]);
  const [sessionStats, setSessionStats] = useState({ correct: 0, total: 0 });
  const [isPlaying, setIsPlaying] = useState(false);
  const [studyMode, setStudyMode] = useState('recognition'); // recognition, writing, listening
  const [selectedLevel, setSelectedLevel] = useState(1);
  const [streakCount, setStreakCount] = useState(0);
  const [traceMode, setTraceMode] = useState(false);
  
  const canvasRef = useRef(null);
  const [isDrawing, setIsDrawing] = useState(false);

  // 初始化学习会话 - 基于间隔重复算法
  const initializeSession = (level) => {
    const levelCards = hanziDatabase.filter(card => card.level <= level);
    // 根据用户表现调整卡片出现频率
    const weightedCards = levelCards.map(card => {
      const progress = userProgress[card.id] || { attempts: 0, correct: 0, lastSeen: 0 };
      const difficulty = Math.max(1, progress.attempts - progress.correct);
      const timeSinceLastSeen = Date.now() - progress.lastSeen;
      const weight = difficulty * Math.max(1, 7 - Math.floor(timeSinceLastSeen / (24 * 60 * 60 * 1000)));
      return { ...card, weight };
    });
    
    // 基于权重随机选择
    const session = [];
    for (let i = 0; i < Math.min(10, weightedCards.length); i++) {
      const totalWeight = weightedCards.reduce((sum, card) => sum + card.weight, 0);
      let random = Math.random() * totalWeight;
      for (const card of weightedCards) {
        random -= card.weight;
        if (random <= 0) {
          session.push(card);
          break;
        }
      }
    }
    
    setStudySession(session);
    setCurrentCard(0);
    setShowAnswer(false);
    setSessionStats({ correct: 0, total: 0 });
  };

  // 语音合成
  const speakChinese = (text) => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = 'zh-CN';
      utterance.rate = 0.8;
      speechSynthesis.speak(utterance);
    }
  };

  // 处理答案反馈
  const handleAnswer = (isCorrect) => {
    const cardId = studySession[currentCard]?.id;
    if (cardId) {
      const currentProgress = userProgress[cardId] || { attempts: 0, correct: 0, lastSeen: 0 };
      const newProgress = {
        attempts: currentProgress.attempts + 1,
        correct: currentProgress.correct + (isCorrect ? 1 : 0),
        lastSeen: Date.now()
      };
      setUserProgress({ ...userProgress, [cardId]: newProgress });
    }
    
    setSessionStats(prev => ({
      correct: prev.correct + (isCorrect ? 1 : 0),
      total: prev.total + 1
    }));

    if (isCorrect) {
      setStreakCount(prev => prev + 1);
    } else {
      setStreakCount(0);
    }

    // 自动前进到下一张卡片
    setTimeout(() => {
      if (currentCard < studySession.length - 1) {
        setCurrentCard(currentCard + 1);
        setShowAnswer(false);
      } else {
        setCurrentScreen('results');
      }
    }, 1500);
  };

  // 绘制功能
  const startDrawing = (e) => {
    if (!traceMode) return;
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    const ctx = canvas.getContext('2d');
    ctx.beginPath();
    ctx.moveTo(x, y);
    setIsDrawing(true);
  };

  const draw = (e) => {
    if (!isDrawing || !traceMode) return;
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    const ctx = canvas.getContext('2d');
    ctx.lineTo(x, y);
    ctx.stroke();
  };

  const stopDrawing = () => {
    setIsDrawing(false);
  };

  const clearCanvas = () => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    ctx.clearRect(0, 0, canvas.width, canvas.height);
  };

  // 主页面组件
  const HomePage = () => (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
      <div className="max-w-md mx-auto">
        {/* 头部 */}
        <div className="text-center mb-8">
          <div className="bg-white rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center shadow-lg">
            <Brain className="w-10 h-10 text-indigo-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">汉字大师</h1>
          <p className="text-gray-600">Smart Chinese Character Learning</p>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-2 gap-4 mb-8">
          <div className="bg-white rounded-2xl p-4 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">学习天数</p>
                <p className="text-2xl font-bold text-indigo-600">12</p>
              </div>
              <Calendar className="w-8 h-8 text-indigo-400" />
            </div>
          </div>
          <div className="bg-white rounded-2xl p-4 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">掌握汉字</p>
                <p className="text-2xl font-bold text-green-600">89</p>
              </div>
              <Trophy className="w-8 h-8 text-green-400" />
            </div>
          </div>
        </div>

        {/* 学习模式选择 */}
        <div className="bg-white rounded-3xl p-6 mb-6 shadow-sm">
          <h2 className="text-xl font-bold text-gray-800 mb-4">选择学习模式</h2>
          
          <div className="space-y-3">
            <button
              onClick={() => {
                setStudyMode('recognition');
                setCurrentScreen('levelSelect');
              }}
              className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-2xl p-4 flex items-center justify-between hover:shadow-lg transition-all"
            >
              <div className="flex items-center">
                <Eye className="w-6 h-6 mr-3" />
                <div className="text-left">
                  <div className="font-semibold">Recognition Mode</div>
                  <div className="text-sm opacity-90">Character recognition practice</div>
                </div>
              </div>
            </button>
            
            <button
              onClick={() => {
                setStudyMode('writing');
                setCurrentScreen('levelSelect');
              }}
              className="w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-2xl p-4 flex items-center justify-between hover:shadow-lg transition-all"
            >
              <div className="flex items-center">
                <PenTool className="w-6 h-6 mr-3" />
                <div className="text-left">
                  <div className="font-semibold">Writing Mode</div>
                  <div className="text-sm opacity-90">Practice writing characters</div>
                </div>
              </div>
            </button>
            
            <button
              onClick={() => {
                setStudyMode('listening');
                setCurrentScreen('levelSelect');
              }}
              className="w-full bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-2xl p-4 flex items-center justify-between hover:shadow-lg transition-all"
            >
              <div className="flex items-center">
                <Volume2 className="w-6 h-6 mr-3" />
                <div className="text-left">
                  <div className="font-semibold">Listening Mode</div>
                  <div className="text-sm opacity-90">Audio-based learning</div>
                </div>
              </div>
            </button>
          </div>
        </div>

        {/* 快速访问 */}
        <div className="flex space-x-3">
          <button
            onClick={() => setCurrentScreen('progress')}
            className="flex-1 bg-white rounded-2xl p-4 flex items-center justify-center shadow-sm hover:shadow-md transition-all"
          >
            <BarChart3 className="w-6 h-6 text-gray-600 mr-2" />
            <span className="font-medium text-gray-700">Progress</span>
          </button>
          <button
            onClick={() => setCurrentScreen('settings')}
            className="flex-1 bg-white rounded-2xl p-4 flex items-center justify-center shadow-sm hover:shadow-md transition-all"
          >
            <Settings className="w-6 h-6 text-gray-600 mr-2" />
            <span className="font-medium text-gray-700">Settings</span>
          </button>
        </div>
      </div>
    </div>
  );

  // 等级选择页面
  const LevelSelectPage = () => (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
      <div className="max-w-md mx-auto">
        <div className="flex items-center mb-6">
          <button
            onClick={() => setCurrentScreen('home')}
            className="p-2 rounded-full bg-white shadow-sm mr-4"
          >
            <RotateCcw className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-2xl font-bold text-gray-800">选择难度等级</h1>
        </div>

        <div className="space-y-4">
          {[1, 2, 3].map(level => (
            <button
              key={level}
              onClick={() => {
                setSelectedLevel(level);
                initializeSession(level);
                setCurrentScreen('study');
              }}
              className="w-full bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-all"
            >
              <div className="flex items-center justify-between">
                <div className="text-left">
                  <div className="text-xl font-bold text-gray-800">HSK Level {level}</div>
                  <div className="text-gray-600 text-sm">
                    {level === 1 && "Basic characters (150 characters)"}
                    {level === 2 && "Elementary level (300 characters)"}
                    {level === 3 && "Intermediate level (600 characters)"}
                  </div>
                  <div className="mt-2">
                    <div className="flex items-center text-sm text-green-600">
                      <Star className="w-4 h-4 mr-1" />
                      Mastered: {hanziDatabase.filter(card => 
                        card.level <= level && 
                        userProgress[card.id]?.correct > 2
                      ).length}
                    </div>
                  </div>
                </div>
                <Target className="w-8 h-8 text-indigo-400" />
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );

  // 学习页面
  const StudyPage = () => {
    if (studySession.length === 0) return null;
    
    const card = studySession[currentCard];
    if (!card) return null;

    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
        <div className="max-w-md mx-auto">
          {/* 头部进度 */}
          <div className="flex items-center justify-between mb-6">
            <button
              onClick={() => setCurrentScreen('home')}
              className="p-2 rounded-full bg-white shadow-sm"
            >
              <RotateCcw className="w-6 h-6 text-gray-600" />
            </button>
            <div className="flex-1 mx-4">
              <div className="bg-white rounded-full h-2 overflow-hidden">
                <div 
                  className="h-full bg-gradient-to-r from-indigo-500 to-purple-600 transition-all duration-300"
                  style={{ width: `${((currentCard + 1) / studySession.length) * 100}%` }}
                />
              </div>
              <div className="text-center text-sm text-gray-600 mt-1">
                {currentCard + 1} / {studySession.length}
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-600">Streak</div>
              <div className="text-lg font-bold text-orange-600">{streakCount}</div>
            </div>
          </div>

          {/* 汉字卡片 */}
          <div className="bg-white rounded-3xl p-8 mb-6 shadow-lg text-center min-h-[400px] flex flex-col justify-center">
            {studyMode === 'recognition' && (
              <>
                <div className="text-8xl font-bold text-gray-800 mb-6">{card.character}</div>
                <button
                  onClick={() => speakChinese(card.character)}
                  className="mx-auto mb-4 p-3 bg-indigo-100 rounded-full hover:bg-indigo-200 transition-colors"
                >
                  <Volume2 className="w-6 h-6 text-indigo-600" />
                </button>
                
                {showAnswer && (
                  <div className="space-y-4 animate-fade-in">
                    <div className="text-2xl font-bold text-indigo-600">{card.pinyin}</div>
                    <div className="text-xl text-gray-700">{card.meaning}</div>
                    <div className="bg-blue-50 rounded-2xl p-4">
                      <div className="text-sm text-gray-600 mb-2">Memory Story:</div>
                      <div className="text-gray-800">{card.story}</div>
                    </div>
                    <div className="text-sm text-gray-500">
                      Strokes: {card.strokes} | Components: {card.components.join(', ')}
                    </div>
                  </div>
                )}
              </>
            )}

            {studyMode === 'writing' && (
              <>
                <div className="text-xl text-gray-700 mb-4">{card.meaning}</div>
                <div className="text-lg text-indigo-600 mb-6">{card.pinyin}</div>
                
                <div className="relative mb-6">
                  <canvas
                    ref={canvasRef}
                    width={200}
                    height={200}
                    className="border-2 border-dashed border-gray-300 rounded-2xl mx-auto bg-gray-50"
                    onMouseDown={startDrawing}
                    onMouseMove={draw}
                    onMouseUp={stopDrawing}
                    onMouseLeave={stopDrawing}
                  />
                  <div className="absolute inset-0 text-9xl text-gray-200 flex items-center justify-center pointer-events-none">
                    {traceMode ? card.character : ''}
                  </div>
                </div>

                <div className="flex space-x-4 justify-center mb-6">
                  <button
                    onClick={() => setTraceMode(!traceMode)}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                      traceMode ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-700'
                    }`}
                  >
                    Trace Mode
                  </button>
                  <button
                    onClick={clearCanvas}
                    className="px-4 py-2 bg-gray-200 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-300 transition-colors"
                  >
                    Clear
                  </button>
                </div>

                {showAnswer && (
                  <div className="animate-fade-in">
                    <div className="text-6xl font-bold text-gray-800 mb-4">{card.character}</div>
                    <div className="bg-blue-50 rounded-2xl p-4">
                      <div className="text-sm text-gray-600 mb-2">Stroke Order & Story:</div>
                      <div className="text-gray-800">{card.story}</div>
                    </div>
                  </div>
                )}
              </>
            )}

            {studyMode === 'listening' && (
              <>
                <div className="mb-8">
                  <button
                    onClick={() => speakChinese(card.character)}
                    className="mx-auto p-6 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full text-white hover:shadow-lg transition-all"
                  >
                    <Volume2 className="w-12 h-12" />
                  </button>
                  <div className="text-gray-600 mt-4">Listen and choose the correct character</div>
                </div>

                {showAnswer && (
                  <div className="space-y-4 animate-fade-in">
                    <div className="text-8xl font-bold text-gray-800">{card.character}</div>
                    <div className="text-2xl font-bold text-indigo-600">{card.pinyin}</div>
                    <div className="text-xl text-gray-700">{card.meaning}</div>
                  </div>
                )}
              </>
            )}
          </div>

          {/* 控制按钮 */}
          {!showAnswer ? (
            <button
              onClick={() => setShowAnswer(true)}
              className="w-full bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-2xl py-4 font-semibold text-lg hover:shadow-lg transition-all"
            >
              Show Answer
            </button>
          ) : (
            <div className="flex space-x-4">
              <button
                onClick={() => handleAnswer(false)}
                className="flex-1 bg-gradient-to-r from-red-500 to-pink-600 text-white rounded-2xl py-4 flex items-center justify-center hover:shadow-lg transition-all"
              >
                <XCircle className="w-6 h-6 mr-2" />
                Hard
              </button>
              <button
                onClick={() => handleAnswer(true)}
                className="flex-1 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-2xl py-4 flex items-center justify-center hover:shadow-lg transition-all"
              >
                <CheckCircle className="w-6 h-6 mr-2" />
                Easy
              </button>
            </div>
          )}
        </div>
      </div>
    );
  };

  // 结果页面
  const ResultsPage = () => (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
      <div className="max-w-md mx-auto text-center">
        <div className="bg-white rounded-3xl p-8 mb-6 shadow-lg">
          <div className="text-6xl mb-4">🎉</div>
          <h2 className="text-3xl font-bold text-gray-800 mb-4">Session Complete!</h2>
          
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-green-50 rounded-2xl p-4">
              <div className="text-2xl font-bold text-green-600">{sessionStats.correct}</div>
              <div className="text-green-700 text-sm">Correct</div>
            </div>
            <div className="bg-blue-50 rounded-2xl p-4">
              <div className="text-2xl font-bold text-blue-600">{sessionStats.total}</div>
              <div className="text-blue-700 text-sm">Total</div>
            </div>
          </div>

          <div className="text-lg text-gray-700 mb-6">
            Accuracy: {Math.round((sessionStats.correct / sessionStats.total) * 100)}%
          </div>

          <div className="space-y-3">
            <button
              onClick={() => {
                initializeSession(selectedLevel);
                setCurrentScreen('study');
              }}
              className="w-full bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-2xl py-3 font-semibold hover:shadow-lg transition-all"
            >
              Study Again
            </button>
            <button
              onClick={() => setCurrentScreen('home')}
              className="w-full bg-gray-200 text-gray-700 rounded-2xl py-3 font-semibold hover:bg-gray-300 transition-all"
            >
              Back to Home
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  // 进度页面
  const ProgressPage = () => (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
      <div className="max-w-md mx-auto">
        <div className="flex items-center mb-6">
          <button
            onClick={() => setCurrentScreen('home')}
            className="p-2 rounded-full bg-white shadow-sm mr-4"
          >
            <RotateCcw className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-2xl font-bold text-gray-800">Learning Progress</h1>
        </div>

        <div className="bg-white rounded-3xl p-6 shadow-lg">
          <h3 className="text-xl font-bold text-gray-800 mb-4">Your Statistics</h3>
          
          {[1, 2, 3].map(level => {
            const levelCards = hanziDatabase.filter(card => card.level === level);
            const masteredCards = levelCards.filter(card => 
              userProgress[card.id]?.correct > 2
            );
            
            return (
              <div key={level} className="mb-6">
                <div className="flex justify-between items-center mb-2">
                  <span className="font-semibold text-gray-700">HSK Level {level}</span>
                  <span className="text-sm text-gray-500">
                    {masteredCards.length}/{levelCards.length}
                  </span>
                </div>
                <div className="bg-gray-200 rounded-full h-3">
                  <div 
                    className="bg-gradient-to-r from-green-400 to-green-600 h-3 rounded-full transition-all duration-300"
                    style={{ width: `${(masteredCards.length / levelCards.length) * 100}%` }}
                  />
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );

  // 设置页面
  const SettingsPage = () => (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
      <div className="max-w-md mx-auto">
        <div className="flex items-center mb-6">
          <button
            onClick={() => setCurrentScreen('home')}
            className="p-2 rounded-full bg-white shadow-sm mr-4"
          >
            <RotateCcw className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-2xl font-bold text-gray-800">Settings</h1>
        </div>

        <div className="bg-white rounded-3xl p-6 shadow-lg">
          <h3 className="text-xl font-bold text-gray-800 mb-4">Learning Preferences</h3>
          
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-700">Audio Pronunciation</span>
              <div className="w-12 h-6 bg-indigo-600 rounded-full flex items-center px-1">
                <div className="w-4 h-4 bg-white rounded-full ml-auto"></div>
              </div>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-gray-700">Show Stroke Order</span>
              <div className="w-12 h-6 bg-gray-300 rounded-full flex items-center px-1">
                <div className="w-4 h-4 bg-white rounded-full"></div>
              </div>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-gray-700">Dark Mode</span>
              <div className="w-12 h-6 bg-gray-300 rounded-full flex items-center px-1">
                <div className="w-4 h-4 bg-white rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // 渲染当前屏幕
  const renderCurrentScreen = () => {
    switch (currentScreen) {
      case 'home':
        return <HomePage />;
      case 'levelSelect':
        return <LevelSelectPage />;
      case 'study':
        return <StudyPage />;
      case 'results':
        return <ResultsPage />;
      case 'progress':
        return <ProgressPage />;
      case 'settings':
        return <SettingsPage />;
      default:
        return <HomePage />;
    }
  };

  return (
    <div className="font-sans">
      {renderCurrentScreen()}
      <style jsx>{`
        @keyframes fade-in {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fade-in {
          animation: fade-in 0.3s ease-out;
        }
      `}</style>
    </div>
  );
};

export default HanziFlashcardApp;